{% extends "base.html" %}

{% block title %}قيد يومي جديد - نظام المحاسبة الشامل{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-plus me-2"></i>
        قيد يومي جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('journal_entries') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للقيود اليومية
            </a>
        </div>
    </div>
</div>

<form method="POST" id="journalForm">
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات القيد
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="entry_number" class="form-label">رقم القيد <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-hashtag"></i>
                                </span>
                                <input type="text" class="form-control" id="entry_number" name="entry_number" required readonly>
                                <button class="btn btn-outline-secondary" type="button" id="generateNumber">
                                    <i class="fas fa-magic"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="entry_date" class="form-label">تاريخ القيد <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="entry_date" name="entry_date" required>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="reference" class="form-label">المرجع</label>
                            <input type="text" class="form-control" id="reference" name="reference" placeholder="رقم المرجع">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label for="description" class="form-label">بيان القيد <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" name="description" rows="2" required placeholder="وصف مفصل للقيد"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- تفاصيل القيد -->
            <div class="card mt-4">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0">
                                <i class="fas fa-list me-2"></i>
                                تفاصيل القيد
                            </h5>
                        </div>
                        <div class="col-auto">
                            <button type="button" class="btn btn-sm btn-success" id="addLine">
                                <i class="fas fa-plus me-1"></i>
                                إضافة سطر
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="entryLinesTable">
                            <thead>
                                <tr>
                                    <th width="30%">الحساب <span class="text-danger">*</span></th>
                                    <th width="35%">البيان</th>
                                    <th width="15%">مدين</th>
                                    <th width="15%">دائن</th>
                                    <th width="5%">حذف</th>
                                </tr>
                            </thead>
                            <tbody id="entryLines">
                                <!-- سيتم إضافة الأسطر هنا ديناميكياً -->
                            </tbody>
                            <tfoot>
                                <tr class="table-secondary">
                                    <th colspan="2">الإجمالي</th>
                                    <th class="text-end" id="totalDebit">0.00</th>
                                    <th class="text-end" id="totalCredit">0.00</th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    
                    <div class="alert alert-info mt-3" id="balanceAlert" style="display: none;">
                        <i class="fas fa-info-circle me-2"></i>
                        <span id="balanceMessage"></span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        ملخص القيد
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <h6 class="text-primary">إجمالي المدين</h6>
                            <h4 class="text-primary" id="summaryDebit">0.00</h4>
                        </div>
                        <div class="col-6">
                            <h6 class="text-warning">إجمالي الدائن</h6>
                            <h4 class="text-warning" id="summaryCredit">0.00</h4>
                        </div>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-12">
                            <h6>حالة التوازن</h6>
                            <h4 id="balanceStatus" class="text-danger">غير متوازن</h4>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row text-center">
                        <div class="col-12">
                            <small class="text-muted">عدد الأسطر</small>
                            <div class="fw-bold" id="lineCount">0</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-search me-2"></i>
                        البحث في الحسابات
                    </h5>
                </div>
                <div class="card-body">
                    <input type="text" class="form-control mb-3" id="accountSearch" placeholder="البحث في الحسابات...">
                    <div id="accountsList" style="max-height: 300px; overflow-y: auto;">
                        {% for account in accounts %}
                        <div class="account-item p-2 border-bottom" data-account-id="{{ account.id }}" data-account-code="{{ account.account_code }}" data-account-name="{{ account.account_name_ar }}">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <strong>{{ account.account_code }}</strong>
                                    <div class="small text-muted">{{ account.account_name_ar }}</div>
                                </div>
                                <span class="badge {% if account.account_type == 'assets' %}bg-success{% elif account.account_type == 'liabilities' %}bg-warning{% elif account.account_type == 'equity' %}bg-info{% elif account.account_type == 'revenue' %}bg-primary{% else %}bg-danger{% endif %}">
                                    {% if account.account_type == 'assets' %}أصول{% elif account.account_type == 'liabilities' %}خصوم{% elif account.account_type == 'equity' %}حقوق ملكية{% elif account.account_type == 'revenue' %}إيرادات{% else %}مصروفات{% endif %}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        نصائح
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <small>
                            <ul class="mb-0">
                                <li>تأكد من توازن القيد (المدين = الدائن)</li>
                                <li>اكتب بيان واضح لكل سطر</li>
                                <li>استخدم المرجع لربط القيد بالمستندات</li>
                                <li>راجع القيد قبل الحفظ</li>
                            </ul>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('journal_entries') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="button" class="btn btn-outline-primary me-md-2" id="saveDraft">
                            <i class="fas fa-save me-1"></i>
                            حفظ كمسودة
                        </button>
                        <button type="submit" class="btn btn-primary" id="saveAndPost">
                            <i class="fas fa-check me-1"></i>
                            حفظ وترحيل
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
let lineCounter = 0;

document.addEventListener('DOMContentLoaded', function() {
    // Set today's date
    document.getElementById('entry_date').value = new Date().toISOString().split('T')[0];
    
    // Generate entry number
    generateEntryNumber();
    
    // Add initial lines
    addEntryLine();
    addEntryLine();
    
    // Account search
    document.getElementById('accountSearch').addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();
        document.querySelectorAll('.account-item').forEach(item => {
            const text = item.textContent.toLowerCase();
            item.style.display = text.includes(searchTerm) ? 'block' : 'none';
        });
    });
    
    // Account selection
    document.querySelectorAll('.account-item').forEach(item => {
        item.addEventListener('click', function() {
            const activeSelect = document.querySelector('.account-select:focus');
            if (activeSelect) {
                const option = new Option(
                    this.dataset.accountCode + ' - ' + this.dataset.accountName,
                    this.dataset.accountId,
                    true,
                    true
                );
                activeSelect.innerHTML = '';
                activeSelect.appendChild(option);
                activeSelect.blur();
            }
        });
    });
});

function generateEntryNumber() {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    document.getElementById('entry_number').value = `JE-${year}${month}${day}-${random}`;
}

document.getElementById('generateNumber').addEventListener('click', generateEntryNumber);

function addEntryLine() {
    lineCounter++;
    const tbody = document.getElementById('entryLines');
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>
            <select class="form-select account-select" name="account_id[]" required>
                <option value="">اختر الحساب</option>
                {% for account in accounts %}
                <option value="{{ account.id }}">{{ account.account_code }} - {{ account.account_name_ar }}</option>
                {% endfor %}
            </select>
        </td>
        <td>
            <input type="text" class="form-control" name="line_description[]" placeholder="بيان السطر">
        </td>
        <td>
            <input type="number" class="form-control debit-input" name="debit_amount[]" min="0" step="0.01" placeholder="0.00">
        </td>
        <td>
            <input type="number" class="form-control credit-input" name="credit_amount[]" min="0" step="0.01" placeholder="0.00">
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeEntryLine(this)">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    
    tbody.appendChild(row);
    
    // Add event listeners for calculation
    row.querySelectorAll('.debit-input, .credit-input').forEach(input => {
        input.addEventListener('input', calculateTotals);
        input.addEventListener('input', function() {
            // Clear the other input in the same row
            const row = this.closest('tr');
            if (this.classList.contains('debit-input')) {
                row.querySelector('.credit-input').value = '';
            } else {
                row.querySelector('.debit-input').value = '';
            }
        });
    });
    
    updateLineCount();
}

function removeEntryLine(button) {
    if (document.querySelectorAll('#entryLines tr').length > 1) {
        button.closest('tr').remove();
        calculateTotals();
        updateLineCount();
    } else {
        alert('يجب أن يحتوي القيد على سطر واحد على الأقل');
    }
}

function calculateTotals() {
    let totalDebit = 0;
    let totalCredit = 0;
    
    document.querySelectorAll('.debit-input').forEach(input => {
        totalDebit += parseFloat(input.value) || 0;
    });
    
    document.querySelectorAll('.credit-input').forEach(input => {
        totalCredit += parseFloat(input.value) || 0;
    });
    
    // Update totals
    document.getElementById('totalDebit').textContent = totalDebit.toFixed(2);
    document.getElementById('totalCredit').textContent = totalCredit.toFixed(2);
    document.getElementById('summaryDebit').textContent = totalDebit.toFixed(2);
    document.getElementById('summaryCredit').textContent = totalCredit.toFixed(2);
    
    // Check balance
    const isBalanced = Math.abs(totalDebit - totalCredit) < 0.01;
    const balanceStatus = document.getElementById('balanceStatus');
    const balanceAlert = document.getElementById('balanceAlert');
    
    if (isBalanced && totalDebit > 0) {
        balanceStatus.textContent = 'متوازن';
        balanceStatus.className = 'text-success';
        balanceAlert.style.display = 'none';
    } else if (totalDebit === 0 && totalCredit === 0) {
        balanceStatus.textContent = 'فارغ';
        balanceStatus.className = 'text-muted';
        balanceAlert.style.display = 'none';
    } else {
        balanceStatus.textContent = 'غير متوازن';
        balanceStatus.className = 'text-danger';
        balanceAlert.style.display = 'block';
        document.getElementById('balanceMessage').textContent = 
            `الفرق: ${Math.abs(totalDebit - totalCredit).toFixed(2)} ريال`;
    }
}

function updateLineCount() {
    document.getElementById('lineCount').textContent = document.querySelectorAll('#entryLines tr').length;
}

document.getElementById('addLine').addEventListener('click', addEntryLine);

// Form validation
document.getElementById('journalForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const totalDebit = parseFloat(document.getElementById('totalDebit').textContent);
    const totalCredit = parseFloat(document.getElementById('totalCredit').textContent);
    
    if (Math.abs(totalDebit - totalCredit) >= 0.01) {
        alert('القيد غير متوازن. يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن.');
        return false;
    }
    
    if (totalDebit === 0) {
        alert('يجب إدخال مبالغ في القيد.');
        return false;
    }
    
    // Check if all lines have accounts
    const accountSelects = document.querySelectorAll('.account-select');
    for (let select of accountSelects) {
        if (!select.value) {
            alert('يجب اختيار حساب لجميع الأسطر.');
            return false;
        }
    }
    
    alert('تم حفظ القيد بنجاح!');
});

document.getElementById('saveDraft').addEventListener('click', function() {
    // Save as draft logic
    alert('تم حفظ القيد كمسودة!');
});
</script>
{% endblock %}
