{% extends "base.html" %}

{% block title %}تفاصيل الحساب - نظام المحاسبة الشامل{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-list-alt me-2"></i>
        تفاصيل الحساب: {{ account.account_name_ar }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('accounts') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة لدليل الحسابات
            </a>
            <a href="#" class="btn btn-primary">
                <i class="fas fa-edit me-1"></i>
                تعديل
            </a>
            <a href="#" class="btn btn-success">
                <i class="fas fa-plus me-1"></i>
                قيد جديد
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- معلومات الحساب الأساسية -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    المعلومات الأساسية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رمز الحساب:</strong></td>
                                <td><span class="badge bg-primary">{{ account.account_code }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>اسم الحساب (عربي):</strong></td>
                                <td>{{ account.account_name_ar }}</td>
                            </tr>
                            <tr>
                                <td><strong>اسم الحساب (إنجليزي):</strong></td>
                                <td>{{ account.account_name_en or '<span class="text-muted">غير محدد</span>'|safe }}</td>
                            </tr>
                            <tr>
                                <td><strong>نوع الحساب:</strong></td>
                                <td>
                                    {% if account.account_type == 'assets' %}
                                        <span class="badge bg-success">أصول</span>
                                    {% elif account.account_type == 'liabilities' %}
                                        <span class="badge bg-warning">خصوم</span>
                                    {% elif account.account_type == 'equity' %}
                                        <span class="badge bg-info">حقوق ملكية</span>
                                    {% elif account.account_type == 'revenue' %}
                                        <span class="badge bg-primary">إيرادات</span>
                                    {% elif account.account_type == 'expenses' %}
                                        <span class="badge bg-danger">مصروفات</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الحساب الأب:</strong></td>
                                <td>
                                    {% if account.parent %}
                                        <a href="#" class="text-decoration-none">
                                            {{ account.parent.account_code }} - {{ account.parent.account_name_ar }}
                                        </a>
                                    {% else %}
                                        <span class="text-muted">حساب رئيسي</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    {% if account.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-danger">غير نشط</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الإنشاء:</strong></td>
                                <td>{{ account.created_at.strftime('%Y-%m-%d') if account.created_at else 'غير محدد' }}</td>
                            </tr>
                            <tr>
                                <td><strong>آخر تعديل:</strong></td>
                                <td>{{ account.updated_at.strftime('%Y-%m-%d') if account.updated_at else 'لم يتم التعديل' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if account.description %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6><strong>وصف الحساب:</strong></h6>
                        <p class="text-muted">{{ account.description }}</p>
                    </div>
                </div>
                {% endif %}
                
                {% if account.children %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6><strong>الحسابات الفرعية:</strong></h6>
                        <div class="d-flex flex-wrap gap-2">
                            {% for child in account.children %}
                            <a href="#" class="badge bg-secondary text-decoration-none">
                                {{ child.account_code }} - {{ child.account_name_ar }}
                            </a>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- قيود الحساب -->
        <div class="card mt-4">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0">
                            <i class="fas fa-journal-whills me-2"></i>
                            قيود الحساب
                        </h5>
                    </div>
                    <div class="col-auto">
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-secondary" id="filterAll">الكل</button>
                            <button type="button" class="btn btn-outline-primary" id="filterDebit">مدين</button>
                            <button type="button" class="btn btn-outline-warning" id="filterCredit">دائن</button>
                        </div>
                        <a href="#" class="btn btn-sm btn-success ms-2">
                            <i class="fas fa-plus me-1"></i>
                            قيد جديد
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if account.journal_entries %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم القيد</th>
                                <th>التاريخ</th>
                                <th>البيان</th>
                                <th>مدين</th>
                                <th>دائن</th>
                                <th>الرصيد</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% set running_balance = 0 %}
                            {% for entry in account.journal_entries %}
                                {% if account.account_type in ['assets', 'expenses'] %}
                                    {% set running_balance = running_balance + entry.debit_amount - entry.credit_amount %}
                                {% else %}
                                    {% set running_balance = running_balance + entry.credit_amount - entry.debit_amount %}
                                {% endif %}
                            <tr class="entry-row" data-type="{% if entry.debit_amount > 0 %}debit{% else %}credit{% endif %}">
                                <td><strong>{{ entry.journal_entry.entry_number }}</strong></td>
                                <td>{{ entry.journal_entry.entry_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ entry.description or entry.journal_entry.description }}</td>
                                <td class="text-end">
                                    {% if entry.debit_amount > 0 %}
                                        <span class="text-primary">{{ "%.2f"|format(entry.debit_amount) }}</span>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td class="text-end">
                                    {% if entry.credit_amount > 0 %}
                                        <span class="text-warning">{{ "%.2f"|format(entry.credit_amount) }}</span>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td class="text-end">
                                    <span class="{% if running_balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                                        {{ "%.2f"|format(running_balance) }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary" title="عرض القيد">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-success" title="طباعة">
                                            <i class="fas fa-print"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-secondary">
                                <th colspan="3">الإجمالي</th>
                                <th class="text-end">{{ "%.2f"|format(account.total_debit or 0) }}</th>
                                <th class="text-end">{{ "%.2f"|format(account.total_credit or 0) }}</th>
                                <th class="text-end">
                                    <span class="{% if account.balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                                        {{ "%.2f"|format(account.balance or 0) }}
                                    </span>
                                </th>
                                <th></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-journal-whills fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد قيود لهذا الحساب</p>
                    <a href="#" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>
                        إنشاء قيد جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- الإحصائيات والمعلومات الجانبية -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    ملخص الحساب
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center mb-3">
                    <div class="col-12">
                        <h2 class="{% if account.balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                            {{ "%.2f"|format(account.balance or 0) }}
                        </h2>
                        <small class="text-muted">الرصيد الحالي (ريال)</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="row text-center mb-3">
                    <div class="col-6">
                        <h4 class="text-primary">{{ "%.0f"|format(account.total_debit or 0) }}</h4>
                        <small class="text-muted">إجمالي المدين</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning">{{ "%.0f"|format(account.total_credit or 0) }}</h4>
                        <small class="text-muted">إجمالي الدائن</small>
                    </div>
                </div>
                
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-info">{{ account.journal_entries|length if account.journal_entries else 0 }}</h4>
                        <small class="text-muted">عدد القيود</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-secondary">{{ account.children|length if account.children else 0 }}</h4>
                        <small class="text-muted">الحسابات الفرعية</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    حركة الحساب (آخر 6 أشهر)
                </h5>
            </div>
            <div class="card-body">
                <canvas id="accountChart" width="400" height="200"></canvas>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    آخر النشاطات
                </h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">آخر قيد</h6>
                            <p class="timeline-text">{{ account.last_entry_date.strftime('%Y-%m-%d') if account.last_entry_date else 'لا توجد قيود' }}</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">آخر تعديل</h6>
                            <p class="timeline-text">{{ account.updated_at.strftime('%Y-%m-%d') if account.updated_at else 'لم يتم التعديل' }}</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">تاريخ الإنشاء</h6>
                            <p class="timeline-text">{{ account.created_at.strftime('%Y-%m-%d') if account.created_at else 'غير محدد' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="#" class="btn btn-outline-success">
                        <i class="fas fa-plus me-1"></i>
                        قيد جديد
                    </a>
                    <a href="#" class="btn btn-outline-primary">
                        <i class="fas fa-file-alt me-1"></i>
                        كشف حساب
                    </a>
                    <a href="#" class="btn btn-outline-info">
                        <i class="fas fa-chart-bar me-1"></i>
                        تقرير الحساب
                    </a>
                    <a href="#" class="btn btn-outline-warning">
                        <i class="fas fa-edit me-1"></i>
                        تعديل الحساب
                    </a>
                    <a href="#" class="btn btn-outline-secondary">
                        <i class="fas fa-download me-1"></i>
                        تصدير البيانات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 17px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}

.timeline-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.timeline-text {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filter entries
    document.getElementById('filterAll').addEventListener('click', function() {
        document.querySelectorAll('.entry-row').forEach(row => {
            row.style.display = '';
        });
        updateFilterButtons(this);
    });
    
    document.getElementById('filterDebit').addEventListener('click', function() {
        document.querySelectorAll('.entry-row').forEach(row => {
            row.style.display = row.dataset.type === 'debit' ? '' : 'none';
        });
        updateFilterButtons(this);
    });
    
    document.getElementById('filterCredit').addEventListener('click', function() {
        document.querySelectorAll('.entry-row').forEach(row => {
            row.style.display = row.dataset.type === 'credit' ? '' : 'none';
        });
        updateFilterButtons(this);
    });
    
    function updateFilterButtons(activeBtn) {
        document.querySelectorAll('#filterAll, #filterDebit, #filterCredit').forEach(btn => {
            btn.classList.remove('btn-primary', 'btn-warning');
            btn.classList.add('btn-outline-secondary');
        });
        
        if (activeBtn.id === 'filterDebit') {
            activeBtn.classList.remove('btn-outline-secondary');
            activeBtn.classList.add('btn-primary');
        } else if (activeBtn.id === 'filterCredit') {
            activeBtn.classList.remove('btn-outline-secondary');
            activeBtn.classList.add('btn-warning');
        } else {
            activeBtn.classList.remove('btn-outline-secondary');
            activeBtn.classList.add('btn-primary');
        }
    }
    
    // Simple chart (placeholder)
    const ctx = document.getElementById('accountChart');
    if (ctx) {
        // This would be replaced with actual chart library like Chart.js
        ctx.style.background = 'linear-gradient(45deg, #f8f9fa, #e9ecef)';
        ctx.style.borderRadius = '8px';
        ctx.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 200px; color: #6c757d;">رسم بياني لحركة الحساب</div>';
    }
});
</script>
{% endblock %}
