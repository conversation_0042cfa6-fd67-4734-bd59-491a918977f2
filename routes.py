# -*- coding: utf-8 -*-
"""
مسارات التطبيق
Application Routes
"""

from flask import render_template, request, redirect, url_for, flash, session, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from app import app, db
from models import *
from datetime import datetime, date
from sqlalchemy import func, desc

# الصفحة الرئيسية
@app.route('/')
@login_required
def index():
    # إحصائيات سريعة للوحة التحكم
    total_customers = Customer.query.filter_by(is_active=True).count()
    total_suppliers = Supplier.query.filter_by(is_active=True).count()
    total_products = Product.query.filter_by(is_active=True).count()
    
    # إجمالي المبيعات هذا الشهر
    current_month = date.today().replace(day=1)
    monthly_sales = db.session.query(func.sum(Invoice.total_amount)).filter(
        Invoice.invoice_date >= current_month,
        Invoice.status != 'cancelled'
    ).scalar() or 0
    
    # آخر الفواتير
    recent_invoices = Invoice.query.order_by(desc(Invoice.created_at)).limit(5).all()
    
    return render_template('dashboard.html',
                         total_customers=total_customers,
                         total_suppliers=total_suppliers,
                         total_products=total_products,
                         monthly_sales=monthly_sales,
                         recent_invoices=recent_invoices)

# تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password) and user.is_active:
            login_user(user)
            user.last_login = datetime.utcnow()
            db.session.commit()
            
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('auth/login.html')

# تسجيل الخروج
@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

# إدارة المستخدمين
@app.route('/users')
@login_required
def users():
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('index'))
    
    users = User.query.all()
    return render_template('users/list.html', users=users)

@app.route('/users/add', methods=['GET', 'POST'])
@login_required
def add_user():
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('index'))
    
    if request.method == 'POST':
        username = request.form['username']
        email = request.form['email']
        full_name = request.form['full_name']
        password = request.form['password']
        role = request.form['role']
        
        # التحقق من عدم وجود المستخدم
        if User.query.filter_by(username=username).first():
            flash('اسم المستخدم موجود بالفعل', 'error')
            return render_template('users/add.html')
        
        if User.query.filter_by(email=email).first():
            flash('البريد الإلكتروني موجود بالفعل', 'error')
            return render_template('users/add.html')
        
        user = User(
            username=username,
            email=email,
            full_name=full_name,
            role=role
        )
        user.set_password(password)
        
        db.session.add(user)
        db.session.commit()
        
        flash('تم إضافة المستخدم بنجاح', 'success')
        return redirect(url_for('users'))
    
    return render_template('users/add.html')

# دليل الحسابات
@app.route('/accounts')
@login_required
def accounts():
    accounts = ChartOfAccounts.query.filter_by(is_active=True).order_by(ChartOfAccounts.account_code).all()
    return render_template('accounts/list.html', accounts=accounts)

@app.route('/accounts/add', methods=['GET', 'POST'])
@login_required
def add_account():
    if request.method == 'POST':
        account_code = request.form['account_code']
        account_name_ar = request.form['account_name_ar']
        account_name_en = request.form.get('account_name_en', '')
        account_type = request.form['account_type']
        parent_id = request.form.get('parent_id') or None
        
        # التحقق من عدم وجود رمز الحساب
        if ChartOfAccounts.query.filter_by(account_code=account_code).first():
            flash('رمز الحساب موجود بالفعل', 'error')
            return render_template('accounts/add.html')
        
        account = ChartOfAccounts(
            account_code=account_code,
            account_name_ar=account_name_ar,
            account_name_en=account_name_en,
            account_type=account_type,
            parent_id=parent_id
        )
        
        db.session.add(account)
        db.session.commit()
        
        flash('تم إضافة الحساب بنجاح', 'success')
        return redirect(url_for('accounts'))
    
    # جلب الحسابات الرئيسية للاختيار كحساب أب
    parent_accounts = ChartOfAccounts.query.filter_by(is_active=True).all()
    return render_template('accounts/add.html', parent_accounts=parent_accounts)

# العملاء
@app.route('/customers')
@login_required
def customers():
    customers = Customer.query.filter_by(is_active=True).all()
    return render_template('customers/list.html', customers=customers)

@app.route('/customers/add', methods=['GET', 'POST'])
@login_required
def add_customer():
    if request.method == 'POST':
        customer_code = request.form['customer_code']
        name = request.form['name']
        email = request.form.get('email', '')
        phone = request.form.get('phone', '')
        address = request.form.get('address', '')
        tax_number = request.form.get('tax_number', '')
        credit_limit = float(request.form.get('credit_limit', 0))
        
        # التحقق من عدم وجود رمز العميل
        if Customer.query.filter_by(customer_code=customer_code).first():
            flash('رمز العميل موجود بالفعل', 'error')
            return render_template('customers/add.html')
        
        customer = Customer(
            customer_code=customer_code,
            name=name,
            email=email,
            phone=phone,
            address=address,
            tax_number=tax_number,
            credit_limit=credit_limit
        )
        
        db.session.add(customer)
        db.session.commit()
        
        flash('تم إضافة العميل بنجاح', 'success')
        return redirect(url_for('customers'))
    
    return render_template('customers/add.html')

# الموردين
@app.route('/suppliers')
@login_required
def suppliers():
    suppliers = Supplier.query.filter_by(is_active=True).all()
    return render_template('suppliers/list.html', suppliers=suppliers)

@app.route('/suppliers/add', methods=['GET', 'POST'])
@login_required
def add_supplier():
    if request.method == 'POST':
        supplier_code = request.form['supplier_code']
        name = request.form['name']
        email = request.form.get('email', '')
        phone = request.form.get('phone', '')
        address = request.form.get('address', '')
        tax_number = request.form.get('tax_number', '')
        
        # التحقق من عدم وجود رمز المورد
        if Supplier.query.filter_by(supplier_code=supplier_code).first():
            flash('رمز المورد موجود بالفعل', 'error')
            return render_template('suppliers/add.html')
        
        supplier = Supplier(
            supplier_code=supplier_code,
            name=name,
            email=email,
            phone=phone,
            address=address,
            tax_number=tax_number
        )
        
        db.session.add(supplier)
        db.session.commit()
        
        flash('تم إضافة المورد بنجاح', 'success')
        return redirect(url_for('suppliers'))
    
    return render_template('suppliers/add.html')
