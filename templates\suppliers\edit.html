{% extends "base.html" %}

{% block title %}تعديل المورد - نظام المحاسبة الشامل{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-truck me-2"></i>
        تعديل المورد: {{ supplier.name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('suppliers') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة لقائمة الموردين
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-truck me-2"></i>
                    تعديل بيانات المورد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="supplierForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="supplier_code" class="form-label">رمز المورد <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-hashtag"></i>
                                </span>
                                <input type="text" class="form-control" id="supplier_code" name="supplier_code" value="{{ supplier.supplier_code }}" required readonly>
                            </div>
                            <div class="form-text">رمز المورد لا يمكن تعديله</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم المورد <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-building"></i>
                                </span>
                                <input type="text" class="form-control" id="name" name="name" value="{{ supplier.name }}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input type="email" class="form-control" id="email" name="email" value="{{ supplier.email or '' }}">
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-phone"></i>
                                </span>
                                <input type="tel" class="form-control" id="phone" name="phone" value="{{ supplier.phone or '' }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="tax_number" class="form-label">الرقم الضريبي</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-receipt"></i>
                                </span>
                                <input type="text" class="form-control" id="tax_number" name="tax_number" value="{{ supplier.tax_number or '' }}">
                            </div>
                            <div class="form-text">الرقم الضريبي للمورد (15 رقم)</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="supplier_type" class="form-label">نوع المورد</label>
                            <select class="form-select" id="supplier_type" name="supplier_type">
                                <option value="">اختر نوع المورد</option>
                                <option value="local" {% if supplier.supplier_type == 'local' %}selected{% endif %}>محلي</option>
                                <option value="international" {% if supplier.supplier_type == 'international' %}selected{% endif %}>دولي</option>
                                <option value="manufacturer" {% if supplier.supplier_type == 'manufacturer' %}selected{% endif %}>مصنع</option>
                                <option value="distributor" {% if supplier.supplier_type == 'distributor' %}selected{% endif %}>موزع</option>
                                <option value="service" {% if supplier.supplier_type == 'service' %}selected{% endif %}>خدمات</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label for="address" class="form-label">العنوان</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-map-marker-alt"></i>
                                </span>
                                <textarea class="form-control" id="address" name="address" rows="3">{{ supplier.address or '' }}</textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="contact_person" class="form-label">الشخص المسؤول</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user-tie"></i>
                                </span>
                                <input type="text" class="form-control" id="contact_person" name="contact_person" value="{{ supplier.contact_person or '' }}">
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="payment_terms" class="form-label">شروط الدفع</label>
                            <select class="form-select" id="payment_terms" name="payment_terms">
                                <option value="">اختر شروط الدفع</option>
                                <option value="cash" {% if supplier.payment_terms == 'cash' %}selected{% endif %}>نقداً</option>
                                <option value="net_30" {% if supplier.payment_terms == 'net_30' %}selected{% endif %}>30 يوم</option>
                                <option value="net_60" {% if supplier.payment_terms == 'net_60' %}selected{% endif %}>60 يوم</option>
                                <option value="net_90" {% if supplier.payment_terms == 'net_90' %}selected{% endif %}>90 يوم</option>
                                <option value="custom" {% if supplier.payment_terms == 'custom' %}selected{% endif %}>مخصص</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {% if supplier.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">
                                    المورد نشط
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('suppliers') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ التعديلات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات المورد
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ supplier.purchase_invoices|length if supplier.purchase_invoices else 0 }}</h4>
                        <small class="text-muted">فواتير الشراء</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ "%.2f"|format(supplier.total_purchases or 0) }}</h4>
                        <small class="text-muted">إجمالي المشتريات</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-warning">{{ "%.2f"|format(supplier.outstanding_amount or 0) }}</h4>
                        <small class="text-muted">المبلغ المستحق</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">{{ supplier.average_payment_days or 0 }}</h4>
                        <small class="text-muted">أيام الدفع المتوسط</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    تاريخ المورد
                </h5>
            </div>
            <div class="card-body">
                <p><strong>تاريخ الإضافة:</strong><br>{{ supplier.created_at.strftime('%Y-%m-%d %H:%M') if supplier.created_at else 'غير محدد' }}</p>
                <p><strong>آخر تعديل:</strong><br>{{ supplier.updated_at.strftime('%Y-%m-%d %H:%M') if supplier.updated_at else 'لم يتم التعديل' }}</p>
                <p><strong>آخر فاتورة شراء:</strong><br>{{ supplier.last_purchase_date.strftime('%Y-%m-%d') if supplier.last_purchase_date else 'لا توجد فواتير' }}</p>
                <p><strong>آخر دفعة:</strong><br>{{ supplier.last_payment_date.strftime('%Y-%m-%d') if supplier.last_payment_date else 'لا توجد دفعات' }}</p>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tags me-2"></i>
                    أنواع الموردين
                </h5>
            </div>
            <div class="card-body">
                <small>
                    <strong>محلي:</strong> موردين داخل المملكة<br>
                    <strong>دولي:</strong> موردين من خارج المملكة<br>
                    <strong>مصنع:</strong> مصانع مباشرة<br>
                    <strong>موزع:</strong> شركات توزيع<br>
                    <strong>خدمات:</strong> مقدمي خدمات
                </small>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تحذير
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <small>
                        <i class="fas fa-info-circle me-1"></i>
                        تأكد من صحة البيانات قبل الحفظ. تعديل بيانات المورد قد يؤثر على فواتير الشراء والتقارير المرتبطة.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Format phone number
    document.getElementById('phone').addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.startsWith('966')) {
            value = '+' + value;
        } else if (value.startsWith('5') && value.length === 9) {
            value = '+966' + value;
        }
        this.value = value;
    });
    
    // Validate tax number
    document.getElementById('tax_number').addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length > 15) {
            value = value.substring(0, 15);
        }
        this.value = value;
        
        // Visual feedback
        if (value.length === 15) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        } else if (value.length > 0) {
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-valid', 'is-invalid');
        }
    });
    
    // Form validation
    document.getElementById('supplierForm').addEventListener('submit', function(e) {
        const name = document.getElementById('name').value;
        const taxNumber = document.getElementById('tax_number').value;
        
        if (!name) {
            e.preventDefault();
            alert('يرجى إدخال اسم المورد');
            return false;
        }
        
        if (taxNumber && taxNumber.length !== 15) {
            e.preventDefault();
            alert('الرقم الضريبي يجب أن يكون 15 رقم');
            return false;
        }
    });
});
</script>
{% endblock %}
