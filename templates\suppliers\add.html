{% extends "base.html" %}

{% block title %}إضافة مورد جديد - نظام المحاسبة الشامل{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-plus me-2"></i>
        إضافة مورد جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('suppliers') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة لقائمة الموردين
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-truck me-2"></i>
                    بيانات المورد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="supplierForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="supplier_code" class="form-label">رمز المورد <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-hashtag"></i>
                                </span>
                                <input type="text" class="form-control" id="supplier_code" name="supplier_code" required>
                                <button class="btn btn-outline-secondary" type="button" id="generateCode">
                                    <i class="fas fa-magic"></i>
                                </button>
                            </div>
                            <div class="form-text">رمز فريد للمورد (مثال: SUPP001)</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم المورد <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-building"></i>
                                </span>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-phone"></i>
                                </span>
                                <input type="tel" class="form-control" id="phone" name="phone" placeholder="+966501234567">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="tax_number" class="form-label">الرقم الضريبي</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-receipt"></i>
                                </span>
                                <input type="text" class="form-control" id="tax_number" name="tax_number" placeholder="300000000000003">
                            </div>
                            <div class="form-text">الرقم الضريبي للمورد (15 رقم)</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="supplier_type" class="form-label">نوع المورد</label>
                            <select class="form-select" id="supplier_type" name="supplier_type">
                                <option value="">اختر نوع المورد</option>
                                <option value="local">محلي</option>
                                <option value="international">دولي</option>
                                <option value="manufacturer">مصنع</option>
                                <option value="distributor">موزع</option>
                                <option value="service">خدمات</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label for="address" class="form-label">العنوان</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-map-marker-alt"></i>
                                </span>
                                <textarea class="form-control" id="address" name="address" rows="3" placeholder="العنوان الكامل للمورد"></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="contact_person" class="form-label">الشخص المسؤول</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user-tie"></i>
                                </span>
                                <input type="text" class="form-control" id="contact_person" name="contact_person">
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="payment_terms" class="form-label">شروط الدفع</label>
                            <select class="form-select" id="payment_terms" name="payment_terms">
                                <option value="">اختر شروط الدفع</option>
                                <option value="cash">نقداً</option>
                                <option value="net_30">30 يوم</option>
                                <option value="net_60">60 يوم</option>
                                <option value="net_90">90 يوم</option>
                                <option value="custom">مخصص</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                <label class="form-check-label" for="is_active">
                                    المورد نشط
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('suppliers') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="button" class="btn btn-outline-primary me-md-2" id="saveAndNew">
                            <i class="fas fa-plus me-1"></i>
                            حفظ وإضافة آخر
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ المورد
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مهمة
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6 class="alert-heading">
                        <i class="fas fa-lightbulb me-1"></i>
                        نصائح
                    </h6>
                    <ul class="mb-0">
                        <li>استخدم رموز واضحة للموردين</li>
                        <li>تأكد من صحة البريد الإلكتروني</li>
                        <li>حدد نوع المورد بدقة</li>
                        <li>أدخل الرقم الضريبي إذا كان متوفراً</li>
                        <li>حدد شروط الدفع المناسبة</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6 class="alert-heading">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        تنبيه
                    </h6>
                    <p class="mb-0">
                        الرقم الضريبي يجب أن يكون 15 رقم للشركات المسجلة في ضريبة القيمة المضافة
                    </p>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">0</h4>
                        <small class="text-muted">فواتير الشراء</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">0.00</h4>
                        <small class="text-muted">إجمالي المشتريات</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-warning">0.00</h4>
                        <small class="text-muted">المبلغ المستحق</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">0</h4>
                        <small class="text-muted">أيام الدفع المتوسط</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tags me-2"></i>
                    أنواع الموردين
                </h5>
            </div>
            <div class="card-body">
                <small>
                    <strong>محلي:</strong> موردين داخل المملكة<br>
                    <strong>دولي:</strong> موردين من خارج المملكة<br>
                    <strong>مصنع:</strong> مصانع مباشرة<br>
                    <strong>موزع:</strong> شركات توزيع<br>
                    <strong>خدمات:</strong> مقدمي خدمات
                </small>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    آخر الموردين المضافين
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">مورد تجريبي 1</h6>
                            <small class="text-muted">SUPP001</small>
                        </div>
                        <small class="text-muted">اليوم</small>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">مورد تجريبي 2</h6>
                            <small class="text-muted">SUPP002</small>
                        </div>
                        <small class="text-muted">أمس</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Generate supplier code automatically
    document.getElementById('generateCode').addEventListener('click', function() {
        const timestamp = Date.now().toString().slice(-6);
        const code = 'SUPP' + timestamp;
        document.getElementById('supplier_code').value = code;
    });
    
    // Auto-generate code when name is entered
    document.getElementById('name').addEventListener('blur', function() {
        const codeField = document.getElementById('supplier_code');
        if (!codeField.value && this.value) {
            const name = this.value.trim();
            const words = name.split(' ');
            let code = 'SUPP';
            
            // Take first 2 letters from each word (max 3 words)
            for (let i = 0; i < Math.min(words.length, 3); i++) {
                if (words[i].length >= 2) {
                    code += words[i].substring(0, 2).toUpperCase();
                }
            }
            
            // Add random number
            code += Math.floor(Math.random() * 100).toString().padStart(2, '0');
            codeField.value = code;
        }
    });
    
    // Format phone number
    document.getElementById('phone').addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.startsWith('966')) {
            value = '+' + value;
        } else if (value.startsWith('5') && value.length === 9) {
            value = '+966' + value;
        }
        this.value = value;
    });
    
    // Validate tax number
    document.getElementById('tax_number').addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length > 15) {
            value = value.substring(0, 15);
        }
        this.value = value;
        
        // Visual feedback
        if (value.length === 15) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        } else if (value.length > 0) {
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-valid', 'is-invalid');
        }
    });
    
    // Save and add new functionality
    document.getElementById('saveAndNew').addEventListener('click', function() {
        const form = document.getElementById('supplierForm');
        if (form.checkValidity()) {
            // Here you would submit the form via AJAX
            alert('تم حفظ المورد بنجاح! يمكنك إضافة مورد آخر.');
            form.reset();
            document.getElementById('is_active').checked = true;
        } else {
            form.reportValidity();
        }
    });
    
    // Form validation
    document.getElementById('supplierForm').addEventListener('submit', function(e) {
        const supplierCode = document.getElementById('supplier_code').value;
        const name = document.getElementById('name').value;
        const taxNumber = document.getElementById('tax_number').value;
        
        if (!supplierCode || !name) {
            e.preventDefault();
            alert('يرجى ملء الحقول المطلوبة');
            return false;
        }
        
        if (taxNumber && taxNumber.length !== 15) {
            e.preventDefault();
            alert('الرقم الضريبي يجب أن يكون 15 رقم');
            return false;
        }
    });
});
</script>
{% endblock %}
