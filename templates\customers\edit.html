{% extends "base.html" %}

{% block title %}تعديل العميل - نظام المحاسبة الشامل{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-edit me-2"></i>
        تعديل العميل: {{ customer.name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('customers') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة لقائمة العملاء
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-address-card me-2"></i>
                    تعديل بيانات العميل
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="customerForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="customer_code" class="form-label">رمز العميل <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-hashtag"></i>
                                </span>
                                <input type="text" class="form-control" id="customer_code" name="customer_code" value="{{ customer.customer_code }}" required readonly>
                            </div>
                            <div class="form-text">رمز العميل لا يمكن تعديله</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم العميل <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input type="text" class="form-control" id="name" name="name" value="{{ customer.name }}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input type="email" class="form-control" id="email" name="email" value="{{ customer.email or '' }}">
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-phone"></i>
                                </span>
                                <input type="tel" class="form-control" id="phone" name="phone" value="{{ customer.phone or '' }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="tax_number" class="form-label">الرقم الضريبي</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-receipt"></i>
                                </span>
                                <input type="text" class="form-control" id="tax_number" name="tax_number" value="{{ customer.tax_number or '' }}">
                            </div>
                            <div class="form-text">الرقم الضريبي للعميل (15 رقم)</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="credit_limit" class="form-label">حد الائتمان (ريال)</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-credit-card"></i>
                                </span>
                                <input type="number" class="form-control" id="credit_limit" name="credit_limit" min="0" step="0.01" value="{{ customer.credit_limit }}">
                                <span class="input-group-text">ريال</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label for="address" class="form-label">العنوان</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-map-marker-alt"></i>
                                </span>
                                <textarea class="form-control" id="address" name="address" rows="3">{{ customer.address or '' }}</textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {% if customer.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">
                                    العميل نشط
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('customers') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ التعديلات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات العميل
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ customer.invoices|length if customer.invoices else 0 }}</h4>
                        <small class="text-muted">الفواتير</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ "%.2f"|format(customer.total_sales or 0) }}</h4>
                        <small class="text-muted">إجمالي المبيعات</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-warning">{{ "%.2f"|format(customer.outstanding_amount or 0) }}</h4>
                        <small class="text-muted">المبلغ المستحق</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">{{ "%.2f"|format(customer.credit_limit - (customer.outstanding_amount or 0)) }}</h4>
                        <small class="text-muted">حد الائتمان المتاح</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    تاريخ العميل
                </h5>
            </div>
            <div class="card-body">
                <p><strong>تاريخ الإضافة:</strong><br>{{ customer.created_at.strftime('%Y-%m-%d %H:%M') if customer.created_at else 'غير محدد' }}</p>
                <p><strong>آخر تعديل:</strong><br>{{ customer.updated_at.strftime('%Y-%m-%d %H:%M') if customer.updated_at else 'لم يتم التعديل' }}</p>
                <p><strong>آخر فاتورة:</strong><br>{{ customer.last_invoice_date.strftime('%Y-%m-%d') if customer.last_invoice_date else 'لا توجد فواتير' }}</p>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تحذير
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <small>
                        <i class="fas fa-info-circle me-1"></i>
                        تأكد من صحة البيانات قبل الحفظ. تعديل بيانات العميل قد يؤثر على الفواتير والتقارير المرتبطة.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Format phone number
    document.getElementById('phone').addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.startsWith('966')) {
            value = '+' + value;
        } else if (value.startsWith('5') && value.length === 9) {
            value = '+966' + value;
        }
        this.value = value;
    });
    
    // Validate tax number
    document.getElementById('tax_number').addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length > 15) {
            value = value.substring(0, 15);
        }
        this.value = value;
        
        // Visual feedback
        if (value.length === 15) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        } else if (value.length > 0) {
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-valid', 'is-invalid');
        }
    });
    
    // Update available credit limit
    document.getElementById('credit_limit').addEventListener('input', function() {
        const limit = parseFloat(this.value) || 0;
        const outstanding = {{ customer.outstanding_amount or 0 }};
        const available = limit - outstanding;
        document.querySelector('.text-info').textContent = available.toFixed(2);
    });
    
    // Form validation
    document.getElementById('customerForm').addEventListener('submit', function(e) {
        const name = document.getElementById('name').value;
        const taxNumber = document.getElementById('tax_number').value;
        
        if (!name) {
            e.preventDefault();
            alert('يرجى إدخال اسم العميل');
            return false;
        }
        
        if (taxNumber && taxNumber.length !== 15) {
            e.preventDefault();
            alert('الرقم الضريبي يجب أن يكون 15 رقم');
            return false;
        }
    });
});
</script>
{% endblock %}
