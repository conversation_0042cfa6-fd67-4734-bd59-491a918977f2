{% extends "base.html" %}

{% block title %}إضافة عميل جديد - نظام المحاسبة الشامل{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-plus me-2"></i>
        إضافة عميل جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('customers') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة لقائمة العملاء
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-address-card me-2"></i>
                    بيانات العميل
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="customerForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="customer_code" class="form-label">رمز العميل <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-hashtag"></i>
                                </span>
                                <input type="text" class="form-control" id="customer_code" name="customer_code" required>
                                <button class="btn btn-outline-secondary" type="button" id="generateCode">
                                    <i class="fas fa-magic"></i>
                                </button>
                            </div>
                            <div class="form-text">رمز فريد للعميل (مثال: CUST001)</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم العميل <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-phone"></i>
                                </span>
                                <input type="tel" class="form-control" id="phone" name="phone" placeholder="+966501234567">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="tax_number" class="form-label">الرقم الضريبي</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-receipt"></i>
                                </span>
                                <input type="text" class="form-control" id="tax_number" name="tax_number" placeholder="300000000000003">
                            </div>
                            <div class="form-text">الرقم الضريبي للعميل (15 رقم)</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="credit_limit" class="form-label">حد الائتمان (ريال)</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-credit-card"></i>
                                </span>
                                <input type="number" class="form-control" id="credit_limit" name="credit_limit" min="0" step="0.01" value="0">
                                <span class="input-group-text">ريال</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label for="address" class="form-label">العنوان</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-map-marker-alt"></i>
                                </span>
                                <textarea class="form-control" id="address" name="address" rows="3" placeholder="العنوان الكامل للعميل"></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                <label class="form-check-label" for="is_active">
                                    العميل نشط
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('customers') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="button" class="btn btn-outline-primary me-md-2" id="saveAndNew">
                            <i class="fas fa-plus me-1"></i>
                            حفظ وإضافة آخر
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ العميل
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مهمة
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6 class="alert-heading">
                        <i class="fas fa-lightbulb me-1"></i>
                        نصائح
                    </h6>
                    <ul class="mb-0">
                        <li>استخدم رموز واضحة للعملاء</li>
                        <li>تأكد من صحة البريد الإلكتروني</li>
                        <li>حدد حد ائتمان مناسب</li>
                        <li>أدخل الرقم الضريبي إذا كان متوفراً</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6 class="alert-heading">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        تنبيه
                    </h6>
                    <p class="mb-0">
                        الرقم الضريبي يجب أن يكون 15 رقم للشركات المسجلة في ضريبة القيمة المضافة
                    </p>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">0</h4>
                        <small class="text-muted">الفواتير</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">0.00</h4>
                        <small class="text-muted">إجمالي المبيعات</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-warning">0.00</h4>
                        <small class="text-muted">المبلغ المستحق</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">0.00</h4>
                        <small class="text-muted">حد الائتمان المتاح</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    آخر العملاء المضافين
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">عميل تجريبي 1</h6>
                            <small class="text-muted">CUST001</small>
                        </div>
                        <small class="text-muted">اليوم</small>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">عميل تجريبي 2</h6>
                            <small class="text-muted">CUST002</small>
                        </div>
                        <small class="text-muted">أمس</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Generate customer code automatically
    document.getElementById('generateCode').addEventListener('click', function() {
        const timestamp = Date.now().toString().slice(-6);
        const code = 'CUST' + timestamp;
        document.getElementById('customer_code').value = code;
    });
    
    // Auto-generate code when name is entered
    document.getElementById('name').addEventListener('blur', function() {
        const codeField = document.getElementById('customer_code');
        if (!codeField.value && this.value) {
            const name = this.value.trim();
            const words = name.split(' ');
            let code = 'CUST';
            
            // Take first 2 letters from each word (max 3 words)
            for (let i = 0; i < Math.min(words.length, 3); i++) {
                if (words[i].length >= 2) {
                    code += words[i].substring(0, 2).toUpperCase();
                }
            }
            
            // Add random number
            code += Math.floor(Math.random() * 100).toString().padStart(2, '0');
            codeField.value = code;
        }
    });
    
    // Format phone number
    document.getElementById('phone').addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.startsWith('966')) {
            value = '+' + value;
        } else if (value.startsWith('5') && value.length === 9) {
            value = '+966' + value;
        }
        this.value = value;
    });
    
    // Validate tax number
    document.getElementById('tax_number').addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        if (value.length > 15) {
            value = value.substring(0, 15);
        }
        this.value = value;
        
        // Visual feedback
        if (value.length === 15) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        } else if (value.length > 0) {
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-valid', 'is-invalid');
        }
    });
    
    // Update available credit limit
    document.getElementById('credit_limit').addEventListener('input', function() {
        const limit = parseFloat(this.value) || 0;
        // Update the quick stats card
        document.querySelector('.text-info').textContent = limit.toFixed(2);
    });
    
    // Save and add new functionality
    document.getElementById('saveAndNew').addEventListener('click', function() {
        const form = document.getElementById('customerForm');
        if (form.checkValidity()) {
            // Here you would submit the form via AJAX
            alert('تم حفظ العميل بنجاح! يمكنك إضافة عميل آخر.');
            form.reset();
            document.getElementById('is_active').checked = true;
        } else {
            form.reportValidity();
        }
    });
    
    // Form validation
    document.getElementById('customerForm').addEventListener('submit', function(e) {
        const customerCode = document.getElementById('customer_code').value;
        const name = document.getElementById('name').value;
        const taxNumber = document.getElementById('tax_number').value;
        
        if (!customerCode || !name) {
            e.preventDefault();
            alert('يرجى ملء الحقول المطلوبة');
            return false;
        }
        
        if (taxNumber && taxNumber.length !== 15) {
            e.preventDefault();
            alert('الرقم الضريبي يجب أن يكون 15 رقم');
            return false;
        }
    });
});
</script>
{% endblock %}
