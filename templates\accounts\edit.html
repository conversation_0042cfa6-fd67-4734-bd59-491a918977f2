{% extends "base.html" %}

{% block title %}تعديل الحساب - نظام المحاسبة الشامل{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-edit me-2"></i>
        تعديل الحساب: {{ account.account_name_ar }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('accounts') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة لدليل الحسابات
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list-alt me-2"></i>
                    تعديل بيانات الحساب
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="accountForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="account_code" class="form-label">رمز الحساب <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-hashtag"></i>
                                </span>
                                <input type="text" class="form-control" id="account_code" name="account_code" value="{{ account.account_code }}" required readonly>
                            </div>
                            <div class="form-text">رمز الحساب لا يمكن تعديله</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="account_type" class="form-label">نوع الحساب <span class="text-danger">*</span></label>
                            <select class="form-select" id="account_type" name="account_type" required>
                                <option value="">اختر نوع الحساب</option>
                                <option value="assets" {% if account.account_type == 'assets' %}selected{% endif %}>الأصول (Assets)</option>
                                <option value="liabilities" {% if account.account_type == 'liabilities' %}selected{% endif %}>الخصوم (Liabilities)</option>
                                <option value="equity" {% if account.account_type == 'equity' %}selected{% endif %}>حقوق الملكية (Equity)</option>
                                <option value="revenue" {% if account.account_type == 'revenue' %}selected{% endif %}>الإيرادات (Revenue)</option>
                                <option value="expenses" {% if account.account_type == 'expenses' %}selected{% endif %}>المصروفات (Expenses)</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="account_name_ar" class="form-label">اسم الحساب (عربي) <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-font"></i>
                                </span>
                                <input type="text" class="form-control" id="account_name_ar" name="account_name_ar" value="{{ account.account_name_ar }}" required>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="account_name_en" class="form-label">اسم الحساب (إنجليزي)</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-font"></i>
                                </span>
                                <input type="text" class="form-control" id="account_name_en" name="account_name_en" value="{{ account.account_name_en or '' }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="parent_id" class="form-label">الحساب الأب</label>
                            <select class="form-select" id="parent_id" name="parent_id">
                                <option value="">حساب رئيسي</option>
                                {% for parent_account in parent_accounts %}
                                    {% if parent_account.id != account.id %}
                                    <option value="{{ parent_account.id }}" {% if account.parent_id == parent_account.id %}selected{% endif %}>
                                        {{ parent_account.account_code }} - {{ parent_account.account_name_ar }}
                                    </option>
                                    {% endif %}
                                {% endfor %}
                            </select>
                            <div class="form-text">اختياري - لإنشاء حساب فرعي</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {% if account.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">
                                    الحساب نشط
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label for="description" class="form-label">وصف الحساب</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="وصف مفصل للحساب (اختياري)">{{ account.description or '' }}</textarea>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('accounts') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ التعديلات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات الحساب
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ account.journal_entries|length if account.journal_entries else 0 }}</h4>
                        <small class="text-muted">القيود</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ "%.2f"|format(account.balance or 0) }}</h4>
                        <small class="text-muted">الرصيد</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-info">{{ "%.2f"|format(account.total_debit or 0) }}</h4>
                        <small class="text-muted">إجمالي المدين</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning">{{ "%.2f"|format(account.total_credit or 0) }}</h4>
                        <small class="text-muted">إجمالي الدائن</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الحساب
                </h5>
            </div>
            <div class="card-body">
                <p><strong>تاريخ الإنشاء:</strong><br>{{ account.created_at.strftime('%Y-%m-%d %H:%M') if account.created_at else 'غير محدد' }}</p>
                <p><strong>آخر تعديل:</strong><br>{{ account.updated_at.strftime('%Y-%m-%d %H:%M') if account.updated_at else 'لم يتم التعديل' }}</p>
                <p><strong>آخر قيد:</strong><br>{{ account.last_entry_date.strftime('%Y-%m-%d') if account.last_entry_date else 'لا توجد قيود' }}</p>
                
                {% if account.children %}
                <p><strong>الحسابات الفرعية:</strong><br>
                    {% for child in account.children %}
                        <span class="badge bg-secondary me-1">{{ child.account_code }}</span>
                    {% endfor %}
                </p>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    أنواع الحسابات
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-success">
                        <i class="fas fa-building me-1"></i>
                        الأصول (Assets)
                    </h6>
                    <small class="text-muted">
                        الموارد التي تملكها الشركة مثل النقدية، المخزون، الأثاث، المباني
                    </small>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-warning">
                        <i class="fas fa-credit-card me-1"></i>
                        الخصوم (Liabilities)
                    </h6>
                    <small class="text-muted">
                        الالتزامات المالية مثل القروض، الحسابات الدائنة، المصروفات المستحقة
                    </small>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-info">
                        <i class="fas fa-user-tie me-1"></i>
                        حقوق الملكية (Equity)
                    </h6>
                    <small class="text-muted">
                        حقوق المالكين في الشركة مثل رأس المال، الأرباح المحتجزة
                    </small>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-primary">
                        <i class="fas fa-chart-line me-1"></i>
                        الإيرادات (Revenue)
                    </h6>
                    <small class="text-muted">
                        الدخل من العمليات التجارية مثل مبيعات البضائع، الخدمات
                    </small>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-danger">
                        <i class="fas fa-chart-line-down me-1"></i>
                        المصروفات (Expenses)
                    </h6>
                    <small class="text-muted">
                        التكاليف المتكبدة لتشغيل الأعمال مثل الرواتب، الإيجار، الكهرباء
                    </small>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تحذير
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <small>
                        <i class="fas fa-info-circle me-1"></i>
                        تأكد من صحة البيانات قبل الحفظ. تعديل بيانات الحساب قد يؤثر على القيود والتقارير المرتبطة.
                    </small>
                </div>
                
                {% if account.journal_entries and account.journal_entries|length > 0 %}
                <div class="alert alert-info">
                    <small>
                        <i class="fas fa-info-circle me-1"></i>
                        هذا الحساب يحتوي على {{ account.journal_entries|length }} قيد. تعديل نوع الحساب قد يؤثر على التقارير المالية.
                    </small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filter parent accounts based on selected type
    document.getElementById('account_type').addEventListener('change', function() {
        const parentSelect = document.getElementById('parent_id');
        const selectedType = this.value;
        
        // Reset parent options
        Array.from(parentSelect.options).forEach(option => {
            if (option.value) {
                option.style.display = '';
            }
        });
        
        // If a type is selected, filter parent accounts to show only same type
        if (selectedType) {
            Array.from(parentSelect.options).forEach(option => {
                if (option.value && option.dataset.type && option.dataset.type !== selectedType) {
                    option.style.display = 'none';
                }
            });
        }
    });
    
    // Form validation
    document.getElementById('accountForm').addEventListener('submit', function(e) {
        const accountType = document.getElementById('account_type').value;
        const accountNameAr = document.getElementById('account_name_ar').value;
        
        if (!accountType || !accountNameAr) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
            return false;
        }
        
        // Check if account has entries and type is being changed
        const originalType = '{{ account.account_type }}';
        if (accountType !== originalType && {{ account.journal_entries|length if account.journal_entries else 0 }} > 0) {
            if (!confirm('هذا الحساب يحتوي على قيود. هل أنت متأكد من تغيير نوع الحساب؟')) {
                e.preventDefault();
                return false;
            }
        }
    });
});
</script>
{% endblock %}
