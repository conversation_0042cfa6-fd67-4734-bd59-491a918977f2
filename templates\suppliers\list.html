{% extends "base.html" %}

{% block title %}إدارة الموردين - نظام المحاسبة الشامل{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-truck me-2"></i>
        إدارة الموردين
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('add_supplier') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة مورد جديد
            </a>
            <button type="button" class="btn btn-outline-secondary">
                <i class="fas fa-download me-1"></i>
                تصدير
            </button>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">
                    <i class="fas fa-address-book me-2"></i>
                    قائمة الموردين
                </h5>
            </div>
            <div class="col-auto">
                <input type="text" class="form-control" placeholder="البحث في الموردين..." id="supplierSearch">
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if suppliers %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رمز المورد</th>
                        <th>اسم المورد</th>
                        <th>البريد الإلكتروني</th>
                        <th>الهاتف</th>
                        <th>الرقم الضريبي</th>
                        <th>الحالة</th>
                        <th>تاريخ الإضافة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for supplier in suppliers %}
                    <tr>
                        <td>
                            <strong>{{ supplier.supplier_code }}</strong>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                    {{ supplier.name[0].upper() }}
                                </div>
                                <div>
                                    <strong>{{ supplier.name }}</strong>
                                    {% if supplier.address %}
                                        <br><small class="text-muted">{{ supplier.address[:50] }}{% if supplier.address|length > 50 %}...{% endif %}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            {% if supplier.email %}
                                <a href="mailto:{{ supplier.email }}" class="text-decoration-none">
                                    {{ supplier.email }}
                                </a>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if supplier.phone %}
                                <a href="tel:{{ supplier.phone }}" class="text-decoration-none">
                                    {{ supplier.phone }}
                                </a>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if supplier.tax_number %}
                                {{ supplier.tax_number }}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if supplier.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-danger">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>{{ supplier.created_at.strftime('%Y-%m-%d') if supplier.created_at else '-' }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info" title="عرض التفاصيل" onclick="viewSupplier('{{ supplier.id }}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-success" title="إنشاء فاتورة شراء">
                                    <i class="fas fa-shopping-cart"></i>
                                </button>
                                <button type="button" class="btn btn-outline-warning" title="تعطيل/تفعيل">
                                    <i class="fas fa-power-off"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" title="حذف" onclick="confirmDelete('{{ supplier.supplier_code }}', '{{ supplier.name }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-truck fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا يوجد موردين</h5>
            <p class="text-muted">ابدأ بإضافة موردين جدد لنظامك</p>
            <a href="{{ url_for('add_supplier') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة مورد جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Supplier Statistics -->
{% if suppliers %}
<div class="row mt-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h5 class="card-title">إجمالي الموردين</h5>
                        <h3>{{ suppliers|length }}</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-truck fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h5 class="card-title">الموردين النشطون</h5>
                        <h3>{{ suppliers|selectattr('is_active', 'equalto', true)|list|length }}</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h5 class="card-title">موردين مسجلين ضريبياً</h5>
                        <h3>{{ suppliers|selectattr('tax_number')|list|length }}</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-receipt fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h5 class="card-title">موردين بدون ضريبة</h5>
                        <h3>{{ suppliers|rejectattr('tax_number')|list|length }}</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Supplier Details Modal -->
<div class="modal fade" id="supplierModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل المورد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="supplierDetails">
                <!-- Supplier details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary">تعديل</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المورد <strong id="deleteSupplierName"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    هذا الإجراء لا يمكن التراجع عنه!
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function viewSupplier(supplierId) {
    // This would typically load supplier details via AJAX
    document.getElementById('supplierDetails').innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `;
    
    var supplierModal = new bootstrap.Modal(document.getElementById('supplierModal'));
    supplierModal.show();
    
    // Simulate loading supplier details
    setTimeout(() => {
        document.getElementById('supplierDetails').innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>معلومات أساسية</h6>
                    <p><strong>رمز المورد:</strong> SUPP001</p>
                    <p><strong>الاسم:</strong> مورد تجريبي</p>
                    <p><strong>البريد:</strong> <EMAIL></p>
                </div>
                <div class="col-md-6">
                    <h6>معلومات إضافية</h6>
                    <p><strong>الهاتف:</strong> +966501234567</p>
                    <p><strong>الرقم الضريبي:</strong> 300000000000003</p>
                    <p><strong>الحالة:</strong> نشط</p>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <h6>العنوان</h6>
                    <p>الرياض، المملكة العربية السعودية</p>
                </div>
            </div>
        `;
    }, 1000);
}

function confirmDelete(supplierCode, supplierName) {
    document.getElementById('deleteSupplierName').textContent = supplierName + ' (' + supplierCode + ')';
    document.getElementById('confirmDeleteBtn').onclick = function() {
        alert('سيتم تنفيذ حذف المورد: ' + supplierName);
        $('#deleteModal').modal('hide');
    };
    
    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    document.getElementById('supplierSearch').addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = document.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });
});
</script>
{% endblock %}
