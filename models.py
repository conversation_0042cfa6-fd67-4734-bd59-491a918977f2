# -*- coding: utf-8 -*-
"""
نماذج قاعدة البيانات
Database Models
"""

from app import db
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date
from decimal import Decimal

# نموذج المستخدمين
class User(UserMixin, db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), default='user')  # admin, accountant, user
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def __repr__(self):
        return f'<User {self.username}>'

# نموذج دليل الحسابات
class ChartOfAccounts(db.Model):
    __tablename__ = 'chart_of_accounts'
    
    id = db.Column(db.Integer, primary_key=True)
    account_code = db.Column(db.String(20), unique=True, nullable=False)
    account_name_ar = db.Column(db.String(100), nullable=False)
    account_name_en = db.Column(db.String(100))
    account_type = db.Column(db.String(20), nullable=False)  # assets, liabilities, equity, revenue, expenses
    parent_id = db.Column(db.Integer, db.ForeignKey('chart_of_accounts.id'))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    children = db.relationship('ChartOfAccounts', backref=db.backref('parent', remote_side=[id]))
    journal_entries = db.relationship('JournalEntryLine', backref='account')
    
    def __repr__(self):
        return f'<Account {self.account_code}: {self.account_name_ar}>'

# نموذج القيود اليومية
class JournalEntry(db.Model):
    __tablename__ = 'journal_entries'
    
    id = db.Column(db.Integer, primary_key=True)
    entry_number = db.Column(db.String(20), unique=True, nullable=False)
    entry_date = db.Column(db.Date, nullable=False, default=date.today)
    description = db.Column(db.Text, nullable=False)
    reference = db.Column(db.String(50))
    total_debit = db.Column(db.Numeric(15, 2), default=0)
    total_credit = db.Column(db.Numeric(15, 2), default=0)
    is_posted = db.Column(db.Boolean, default=False)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    lines = db.relationship('JournalEntryLine', backref='journal_entry', cascade='all, delete-orphan')
    creator = db.relationship('User', backref='journal_entries')
    
    def __repr__(self):
        return f'<JournalEntry {self.entry_number}>'

# نموذج تفاصيل القيود اليومية
class JournalEntryLine(db.Model):
    __tablename__ = 'journal_entry_lines'
    
    id = db.Column(db.Integer, primary_key=True)
    journal_entry_id = db.Column(db.Integer, db.ForeignKey('journal_entries.id'), nullable=False)
    account_id = db.Column(db.Integer, db.ForeignKey('chart_of_accounts.id'), nullable=False)
    description = db.Column(db.String(200))
    debit_amount = db.Column(db.Numeric(15, 2), default=0)
    credit_amount = db.Column(db.Numeric(15, 2), default=0)
    
    def __repr__(self):
        return f'<JournalEntryLine {self.id}>'

# نموذج العملاء
class Customer(db.Model):
    __tablename__ = 'customers'
    
    id = db.Column(db.Integer, primary_key=True)
    customer_code = db.Column(db.String(20), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    address = db.Column(db.Text)
    tax_number = db.Column(db.String(20))
    credit_limit = db.Column(db.Numeric(15, 2), default=0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    invoices = db.relationship('Invoice', backref='customer')
    
    def __repr__(self):
        return f'<Customer {self.customer_code}: {self.name}>'

# نموذج الموردين
class Supplier(db.Model):
    __tablename__ = 'suppliers'
    
    id = db.Column(db.Integer, primary_key=True)
    supplier_code = db.Column(db.String(20), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    address = db.Column(db.Text)
    tax_number = db.Column(db.String(20))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    purchase_invoices = db.relationship('PurchaseInvoice', backref='supplier')
    
    def __repr__(self):
        return f'<Supplier {self.supplier_code}: {self.name}>'

# نموذج المنتجات
class Product(db.Model):
    __tablename__ = 'products'

    id = db.Column(db.Integer, primary_key=True)
    product_code = db.Column(db.String(20), unique=True, nullable=False)
    name_ar = db.Column(db.String(100), nullable=False)
    name_en = db.Column(db.String(100))
    description = db.Column(db.Text)
    unit = db.Column(db.String(20), default='قطعة')
    cost_price = db.Column(db.Numeric(15, 2), default=0)
    selling_price = db.Column(db.Numeric(15, 2), default=0)
    current_stock = db.Column(db.Numeric(15, 3), default=0)
    minimum_stock = db.Column(db.Numeric(15, 3), default=0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    invoice_lines = db.relationship('InvoiceLine', backref='product')
    purchase_lines = db.relationship('PurchaseInvoiceLine', backref='product')
    stock_movements = db.relationship('StockMovement', backref='product')

    def __repr__(self):
        return f'<Product {self.product_code}: {self.name_ar}>'

# نموذج فواتير البيع
class Invoice(db.Model):
    __tablename__ = 'invoices'

    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(20), unique=True, nullable=False)
    invoice_date = db.Column(db.Date, nullable=False, default=date.today)
    customer_id = db.Column(db.Integer, db.ForeignKey('customers.id'), nullable=False)
    subtotal = db.Column(db.Numeric(15, 2), default=0)
    discount_amount = db.Column(db.Numeric(15, 2), default=0)
    tax_amount = db.Column(db.Numeric(15, 2), default=0)
    total_amount = db.Column(db.Numeric(15, 2), default=0)
    status = db.Column(db.String(20), default='draft')  # draft, sent, paid, cancelled
    notes = db.Column(db.Text)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    lines = db.relationship('InvoiceLine', backref='invoice', cascade='all, delete-orphan')
    creator = db.relationship('User', backref='invoices')

    def __repr__(self):
        return f'<Invoice {self.invoice_number}>'

# نموذج تفاصيل فواتير البيع
class InvoiceLine(db.Model):
    __tablename__ = 'invoice_lines'

    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    quantity = db.Column(db.Numeric(15, 3), nullable=False)
    unit_price = db.Column(db.Numeric(15, 2), nullable=False)
    discount_percentage = db.Column(db.Numeric(5, 2), default=0)
    line_total = db.Column(db.Numeric(15, 2), nullable=False)

    def __repr__(self):
        return f'<InvoiceLine {self.id}>'

# نموذج فواتير الشراء
class PurchaseInvoice(db.Model):
    __tablename__ = 'purchase_invoices'

    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(20), unique=True, nullable=False)
    invoice_date = db.Column(db.Date, nullable=False, default=date.today)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=False)
    subtotal = db.Column(db.Numeric(15, 2), default=0)
    tax_amount = db.Column(db.Numeric(15, 2), default=0)
    total_amount = db.Column(db.Numeric(15, 2), default=0)
    status = db.Column(db.String(20), default='draft')  # draft, received, paid
    notes = db.Column(db.Text)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    lines = db.relationship('PurchaseInvoiceLine', backref='purchase_invoice', cascade='all, delete-orphan')
    creator = db.relationship('User', backref='purchase_invoices')

    def __repr__(self):
        return f'<PurchaseInvoice {self.invoice_number}>'

# نموذج تفاصيل فواتير الشراء
class PurchaseInvoiceLine(db.Model):
    __tablename__ = 'purchase_invoice_lines'

    id = db.Column(db.Integer, primary_key=True)
    purchase_invoice_id = db.Column(db.Integer, db.ForeignKey('purchase_invoices.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    quantity = db.Column(db.Numeric(15, 3), nullable=False)
    unit_cost = db.Column(db.Numeric(15, 2), nullable=False)
    line_total = db.Column(db.Numeric(15, 2), nullable=False)

    def __repr__(self):
        return f'<PurchaseInvoiceLine {self.id}>'

# نموذج حركات المخزون
class StockMovement(db.Model):
    __tablename__ = 'stock_movements'

    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    movement_type = db.Column(db.String(20), nullable=False)  # in, out, adjustment
    quantity = db.Column(db.Numeric(15, 3), nullable=False)
    unit_cost = db.Column(db.Numeric(15, 2))
    reference_type = db.Column(db.String(20))  # invoice, purchase, adjustment
    reference_id = db.Column(db.Integer)
    notes = db.Column(db.Text)
    movement_date = db.Column(db.Date, nullable=False, default=date.today)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    creator = db.relationship('User', backref='stock_movements')

    def __repr__(self):
        return f'<StockMovement {self.id}>'

# نموذج الأصول الثابتة
class FixedAsset(db.Model):
    __tablename__ = 'fixed_assets'

    id = db.Column(db.Integer, primary_key=True)
    asset_code = db.Column(db.String(20), unique=True, nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    category = db.Column(db.String(50))
    purchase_date = db.Column(db.Date, nullable=False)
    purchase_cost = db.Column(db.Numeric(15, 2), nullable=False)
    useful_life_years = db.Column(db.Integer, nullable=False)
    depreciation_method = db.Column(db.String(20), default='straight_line')
    accumulated_depreciation = db.Column(db.Numeric(15, 2), default=0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<FixedAsset {self.asset_code}: {self.name}>'
