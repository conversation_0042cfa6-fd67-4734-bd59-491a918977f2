{% extends "base.html" %}

{% block title %}إضافة حساب جديد - نظام المحاسبة الشامل{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-plus me-2"></i>
        إضافة حساب جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('accounts') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة لدليل الحسابات
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list-alt me-2"></i>
                    بيانات الحساب
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="accountForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="account_code" class="form-label">رمز الحساب <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-hashtag"></i>
                                </span>
                                <input type="text" class="form-control" id="account_code" name="account_code" required>
                            </div>
                            <div class="form-text">رمز فريد للحساب (مثال: 1001, 2001)</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="account_type" class="form-label">نوع الحساب <span class="text-danger">*</span></label>
                            <select class="form-select" id="account_type" name="account_type" required>
                                <option value="">اختر نوع الحساب</option>
                                <option value="assets">الأصول (Assets)</option>
                                <option value="liabilities">الخصوم (Liabilities)</option>
                                <option value="equity">حقوق الملكية (Equity)</option>
                                <option value="revenue">الإيرادات (Revenue)</option>
                                <option value="expenses">المصروفات (Expenses)</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="account_name_ar" class="form-label">اسم الحساب (عربي) <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-font"></i>
                                </span>
                                <input type="text" class="form-control" id="account_name_ar" name="account_name_ar" required>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="account_name_en" class="form-label">اسم الحساب (إنجليزي)</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-font"></i>
                                </span>
                                <input type="text" class="form-control" id="account_name_en" name="account_name_en">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="parent_id" class="form-label">الحساب الأب</label>
                            <select class="form-select" id="parent_id" name="parent_id">
                                <option value="">حساب رئيسي</option>
                                {% for parent_account in parent_accounts %}
                                <option value="{{ parent_account.id }}">
                                    {{ parent_account.account_code }} - {{ parent_account.account_name_ar }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">اختياري - لإنشاء حساب فرعي</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                <label class="form-check-label" for="is_active">
                                    الحساب نشط
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('accounts') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ الحساب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    أنواع الحسابات
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-success">
                        <i class="fas fa-building me-1"></i>
                        الأصول (Assets)
                    </h6>
                    <small class="text-muted">
                        الموارد التي تملكها الشركة مثل النقدية، المخزون، الأثاث، المباني
                    </small>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-warning">
                        <i class="fas fa-credit-card me-1"></i>
                        الخصوم (Liabilities)
                    </h6>
                    <small class="text-muted">
                        الالتزامات المالية مثل القروض، الحسابات الدائنة، المصروفات المستحقة
                    </small>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-info">
                        <i class="fas fa-user-tie me-1"></i>
                        حقوق الملكية (Equity)
                    </h6>
                    <small class="text-muted">
                        حقوق المالكين في الشركة مثل رأس المال، الأرباح المحتجزة
                    </small>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-primary">
                        <i class="fas fa-chart-line me-1"></i>
                        الإيرادات (Revenue)
                    </h6>
                    <small class="text-muted">
                        الدخل من العمليات التجارية مثل مبيعات البضائع، الخدمات
                    </small>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-danger">
                        <i class="fas fa-chart-line-down me-1"></i>
                        المصروفات (Expenses)
                    </h6>
                    <small class="text-muted">
                        التكاليف المتكبدة لتشغيل الأعمال مثل الرواتب، الإيجار، الكهرباء
                    </small>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        استخدم أرقام متسلسلة للحسابات
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        ابدأ الأصول بـ 1، الخصوم بـ 2، إلخ
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        استخدم أسماء واضحة ومفهومة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        أنشئ حسابات فرعية للتفصيل
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    أمثلة على الحسابات
                </h5>
            </div>
            <div class="card-body">
                <small>
                    <strong>الأصول:</strong><br>
                    1001 - النقدية<br>
                    1002 - البنك<br>
                    1101 - المخزون<br><br>
                    
                    <strong>الخصوم:</strong><br>
                    2001 - الحسابات الدائنة<br>
                    2101 - القروض قصيرة الأجل<br><br>
                    
                    <strong>الإيرادات:</strong><br>
                    4001 - مبيعات البضائع<br>
                    4002 - إيرادات الخدمات<br><br>
                    
                    <strong>المصروفات:</strong><br>
                    5001 - الرواتب<br>
                    5002 - الإيجار
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-suggest account codes based on type
    document.getElementById('account_type').addEventListener('change', function() {
        const accountCodeField = document.getElementById('account_code');
        const type = this.value;
        
        // Suggest starting numbers based on account type
        const suggestions = {
            'assets': '1',
            'liabilities': '2',
            'equity': '3',
            'revenue': '4',
            'expenses': '5'
        };
        
        if (suggestions[type] && !accountCodeField.value) {
            accountCodeField.placeholder = `مثال: ${suggestions[type]}001`;
        }
    });
    
    // Filter parent accounts based on selected type
    document.getElementById('account_type').addEventListener('change', function() {
        const parentSelect = document.getElementById('parent_id');
        const selectedType = this.value;
        
        // Reset parent options
        Array.from(parentSelect.options).forEach(option => {
            if (option.value) {
                option.style.display = '';
            }
        });
        
        // If a type is selected, filter parent accounts to show only same type
        if (selectedType) {
            Array.from(parentSelect.options).forEach(option => {
                if (option.value && option.dataset.type && option.dataset.type !== selectedType) {
                    option.style.display = 'none';
                }
            });
        }
    });
    
    // Validate account code format
    document.getElementById('account_code').addEventListener('input', function() {
        const value = this.value;
        // Remove any non-numeric characters
        this.value = value.replace(/[^0-9]/g, '');
    });
    
    // Form validation
    document.getElementById('accountForm').addEventListener('submit', function(e) {
        const accountCode = document.getElementById('account_code').value;
        const accountType = document.getElementById('account_type').value;
        const accountNameAr = document.getElementById('account_name_ar').value;
        
        if (!accountCode || !accountType || !accountNameAr) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
            return false;
        }
        
        if (accountCode.length < 3) {
            e.preventDefault();
            alert('رمز الحساب يجب أن يكون 3 أرقام على الأقل');
            return false;
        }
    });
});
</script>
{% endblock %}
