{% extends "base.html" %}

{% block title %}إدارة العملاء - نظام المحاسبة الشامل{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users me-2"></i>
        إدارة العملاء
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('add_customer') }}" class="btn btn-primary">
                <i class="fas fa-user-plus me-1"></i>
                إضافة عميل جديد
            </a>
            <button type="button" class="btn btn-outline-secondary">
                <i class="fas fa-download me-1"></i>
                تصدير
            </button>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">
                    <i class="fas fa-address-book me-2"></i>
                    قائمة العملاء
                </h5>
            </div>
            <div class="col-auto">
                <input type="text" class="form-control" placeholder="البحث في العملاء..." id="customerSearch">
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if customers %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رمز العميل</th>
                        <th>اسم العميل</th>
                        <th>البريد الإلكتروني</th>
                        <th>الهاتف</th>
                        <th>الرقم الضريبي</th>
                        <th>حد الائتمان</th>
                        <th>الحالة</th>
                        <th>تاريخ الإضافة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for customer in customers %}
                    <tr>
                        <td>
                            <strong>{{ customer.customer_code }}</strong>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                    {{ customer.name[0].upper() }}
                                </div>
                                <div>
                                    <strong>{{ customer.name }}</strong>
                                    {% if customer.address %}
                                        <br><small class="text-muted">{{ customer.address[:50] }}{% if customer.address|length > 50 %}...{% endif %}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            {% if customer.email %}
                                <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                    {{ customer.email }}
                                </a>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if customer.phone %}
                                <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                    {{ customer.phone }}
                                </a>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if customer.tax_number %}
                                {{ customer.tax_number }}
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-info">{{ "%.2f"|format(customer.credit_limit) }} ريال</span>
                        </td>
                        <td>
                            {% if customer.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-danger">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>{{ customer.created_at.strftime('%Y-%m-%d') if customer.created_at else '-' }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info" title="عرض التفاصيل" onclick="viewCustomer('{{ customer.id }}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-success" title="إنشاء فاتورة">
                                    <i class="fas fa-file-invoice"></i>
                                </button>
                                <button type="button" class="btn btn-outline-warning" title="تعطيل/تفعيل">
                                    <i class="fas fa-power-off"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" title="حذف" onclick="confirmDelete('{{ customer.customer_code }}', '{{ customer.name }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا يوجد عملاء</h5>
            <p class="text-muted">ابدأ بإضافة عملاء جدد لنظامك</p>
            <a href="{{ url_for('add_customer') }}" class="btn btn-primary">
                <i class="fas fa-user-plus me-1"></i>
                إضافة عميل جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Customer Statistics -->
{% if customers %}
<div class="row mt-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h5 class="card-title">إجمالي العملاء</h5>
                        <h3>{{ customers|length }}</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h5 class="card-title">العملاء النشطون</h5>
                        <h3>{{ customers|selectattr('is_active', 'equalto', true)|list|length }}</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-check fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h5 class="card-title">إجمالي حدود الائتمان</h5>
                        <h3>{{ "%.0f"|format(customers|sum(attribute='credit_limit')) }}</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-credit-card fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h5 class="card-title">عملاء بدون ضريبة</h5>
                        <h3>{{ customers|rejectattr('tax_number')|list|length }}</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Customer Details Modal -->
<div class="modal fade" id="customerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل العميل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="customerDetails">
                <!-- Customer details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary">تعديل</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف العميل <strong id="deleteCustomerName"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    هذا الإجراء لا يمكن التراجع عنه!
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function viewCustomer(customerId) {
    // This would typically load customer details via AJAX
    document.getElementById('customerDetails').innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `;
    
    var customerModal = new bootstrap.Modal(document.getElementById('customerModal'));
    customerModal.show();
    
    // Simulate loading customer details
    setTimeout(() => {
        document.getElementById('customerDetails').innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>معلومات أساسية</h6>
                    <p><strong>رمز العميل:</strong> CUST001</p>
                    <p><strong>الاسم:</strong> عميل تجريبي</p>
                    <p><strong>البريد:</strong> <EMAIL></p>
                </div>
                <div class="col-md-6">
                    <h6>معلومات إضافية</h6>
                    <p><strong>الهاتف:</strong> +966501234567</p>
                    <p><strong>حد الائتمان:</strong> 10,000 ريال</p>
                    <p><strong>الحالة:</strong> نشط</p>
                </div>
            </div>
        `;
    }, 1000);
}

function confirmDelete(customerCode, customerName) {
    document.getElementById('deleteCustomerName').textContent = customerName + ' (' + customerCode + ')';
    document.getElementById('confirmDeleteBtn').onclick = function() {
        alert('سيتم تنفيذ حذف العميل: ' + customerName);
        $('#deleteModal').modal('hide');
    };
    
    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    document.getElementById('customerSearch').addEventListener('keyup', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = document.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });
});
</script>
{% endblock %}
