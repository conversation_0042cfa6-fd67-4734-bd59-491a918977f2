{% extends "base.html" %}

{% block title %}إدارة المنتجات - نظام المحاسبة الشامل{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-boxes me-2"></i>
        إدارة المنتجات
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('add_product') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة منتج جديد
            </a>
            <button type="button" class="btn btn-outline-secondary">
                <i class="fas fa-download me-1"></i>
                تصدير
            </button>
            <button type="button" class="btn btn-outline-info">
                <i class="fas fa-warehouse me-1"></i>
                تقرير المخزون
            </a>
        </div>
    </div>
</div>

<!-- فلاتر المنتجات -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-end">
                    <div class="col-md-3">
                        <label for="categoryFilter" class="form-label">الفئة</label>
                        <select class="form-select" id="categoryFilter">
                            <option value="">جميع الفئات</option>
                            <option value="electronics">إلكترونيات</option>
                            <option value="clothing">ملابس</option>
                            <option value="food">أغذية</option>
                            <option value="books">كتب</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="stockFilter" class="form-label">حالة المخزون</label>
                        <select class="form-select" id="stockFilter">
                            <option value="">جميع المنتجات</option>
                            <option value="in_stock">متوفر</option>
                            <option value="low_stock">مخزون منخفض</option>
                            <option value="out_of_stock">نفد المخزون</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="productSearch" class="form-label">البحث</label>
                        <input type="text" class="form-control" id="productSearch" placeholder="البحث في المنتجات...">
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-outline-secondary w-100" id="clearFilters">
                            <i class="fas fa-times me-1"></i>
                            مسح الفلاتر
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة المنتجات
                </h5>
            </div>
            <div class="col-auto">
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-secondary active" id="viewGrid">
                        <i class="fas fa-th"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="viewTable">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if products %}
        <!-- Grid View -->
        <div id="gridView" class="row">
            {% for product in products %}
            <div class="col-lg-4 col-md-6 mb-4 product-card" 
                 data-category="{{ product.category or 'other' }}" 
                 data-stock="{% if product.current_stock <= 0 %}out_of_stock{% elif product.current_stock <= product.minimum_stock %}low_stock{% else %}in_stock{% endif %}">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">{{ product.product_code }}</h6>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="fas fa-eye me-2"></i>عرض</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-edit me-2"></i>تعديل</a></li>
                                <li><a class="dropdown-item" href="#"><i class="fas fa-warehouse me-2"></i>تعديل المخزون</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-trash me-2"></i>حذف</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <h6 class="card-title">{{ product.name_ar }}</h6>
                        {% if product.name_en %}
                        <p class="card-text text-muted small">{{ product.name_en }}</p>
                        {% endif %}
                        
                        <div class="row text-center mb-3">
                            <div class="col-6">
                                <small class="text-muted">سعر التكلفة</small>
                                <div class="fw-bold text-warning">{{ "%.2f"|format(product.cost_price) }}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">سعر البيع</small>
                                <div class="fw-bold text-success">{{ "%.2f"|format(product.selling_price) }}</div>
                            </div>
                        </div>
                        
                        <div class="row text-center">
                            <div class="col-6">
                                <small class="text-muted">المخزون الحالي</small>
                                <div class="fw-bold {% if product.current_stock <= 0 %}text-danger{% elif product.current_stock <= product.minimum_stock %}text-warning{% else %}text-primary{% endif %}">
                                    {{ product.current_stock }} {{ product.unit }}
                                </div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">الحد الأدنى</small>
                                <div class="fw-bold text-info">{{ product.minimum_stock }} {{ product.unit }}</div>
                            </div>
                        </div>
                        
                        {% if product.current_stock <= product.minimum_stock %}
                        <div class="alert alert-warning mt-2 py-1">
                            <small><i class="fas fa-exclamation-triangle me-1"></i>مخزون منخفض</small>
                        </div>
                        {% endif %}
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge {% if product.is_active %}bg-success{% else %}bg-danger{% endif %}">
                                {% if product.is_active %}نشط{% else %}غير نشط{% endif %}
                            </span>
                            <small class="text-muted">{{ product.created_at.strftime('%Y-%m-%d') if product.created_at else '' }}</small>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- Table View -->
        <div id="tableView" style="display: none;">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رمز المنتج</th>
                            <th>اسم المنتج</th>
                            <th>الوحدة</th>
                            <th>سعر التكلفة</th>
                            <th>سعر البيع</th>
                            <th>المخزون الحالي</th>
                            <th>الحد الأدنى</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in products %}
                        <tr class="product-row" 
                            data-category="{{ product.category or 'other' }}" 
                            data-stock="{% if product.current_stock <= 0 %}out_of_stock{% elif product.current_stock <= product.minimum_stock %}low_stock{% else %}in_stock{% endif %}">
                            <td><strong>{{ product.product_code }}</strong></td>
                            <td>
                                <div>
                                    <strong>{{ product.name_ar }}</strong>
                                    {% if product.name_en %}
                                        <br><small class="text-muted">{{ product.name_en }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>{{ product.unit }}</td>
                            <td class="text-end">{{ "%.2f"|format(product.cost_price) }} ريال</td>
                            <td class="text-end">{{ "%.2f"|format(product.selling_price) }} ريال</td>
                            <td class="text-end">
                                <span class="{% if product.current_stock <= 0 %}text-danger{% elif product.current_stock <= product.minimum_stock %}text-warning{% else %}text-primary{% endif %}">
                                    {{ product.current_stock }}
                                </span>
                            </td>
                            <td class="text-end">{{ product.minimum_stock }}</td>
                            <td>
                                {% if product.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                {% else %}
                                    <span class="badge bg-danger">غير نشط</span>
                                {% endif %}
                                
                                {% if product.current_stock <= 0 %}
                                    <span class="badge bg-danger ms-1">نفد</span>
                                {% elif product.current_stock <= product.minimum_stock %}
                                    <span class="badge bg-warning ms-1">منخفض</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-success" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-info" title="تعديل المخزون">
                                        <i class="fas fa-warehouse"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد منتجات</h5>
            <p class="text-muted">ابدأ بإضافة منتجات جديدة لمخزونك</p>
            <a href="{{ url_for('add_product') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة منتج جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- إحصائيات المنتجات -->
{% if products %}
<div class="row mt-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h5 class="card-title">إجمالي المنتجات</h5>
                        <h3>{{ products|length }}</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-boxes fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h5 class="card-title">منتجات متوفرة</h5>
                        <h3>{{ products|selectattr('current_stock', '>', 0)|list|length }}</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h5 class="card-title">مخزون منخفض</h5>
                        <h3 id="lowStockCount">0</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h5 class="card-title">نفد المخزون</h5>
                        <h3>{{ products|selectattr('current_stock', '<=', 0)|list|length }}</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-times-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calculate low stock count
    let lowStockCount = 0;
    {% for product in products %}
        {% if product.current_stock > 0 and product.current_stock <= product.minimum_stock %}
            lowStockCount++;
        {% endif %}
    {% endfor %}
    document.getElementById('lowStockCount').textContent = lowStockCount;
    
    // View toggle
    document.getElementById('viewGrid').addEventListener('click', function() {
        document.getElementById('gridView').style.display = 'block';
        document.getElementById('tableView').style.display = 'none';
        this.classList.add('active');
        document.getElementById('viewTable').classList.remove('active');
    });
    
    document.getElementById('viewTable').addEventListener('click', function() {
        document.getElementById('gridView').style.display = 'none';
        document.getElementById('tableView').style.display = 'block';
        this.classList.add('active');
        document.getElementById('viewGrid').classList.remove('active');
    });
    
    // Filters
    function applyFilters() {
        const categoryFilter = document.getElementById('categoryFilter').value;
        const stockFilter = document.getElementById('stockFilter').value;
        const searchTerm = document.getElementById('productSearch').value.toLowerCase();
        
        // Grid view
        document.querySelectorAll('.product-card').forEach(card => {
            const category = card.dataset.category;
            const stock = card.dataset.stock;
            const text = card.textContent.toLowerCase();
            
            let show = true;
            
            if (categoryFilter && category !== categoryFilter) show = false;
            if (stockFilter && stock !== stockFilter) show = false;
            if (searchTerm && !text.includes(searchTerm)) show = false;
            
            card.style.display = show ? 'block' : 'none';
        });
        
        // Table view
        document.querySelectorAll('.product-row').forEach(row => {
            const category = row.dataset.category;
            const stock = row.dataset.stock;
            const text = row.textContent.toLowerCase();
            
            let show = true;
            
            if (categoryFilter && category !== categoryFilter) show = false;
            if (stockFilter && stock !== stockFilter) show = false;
            if (searchTerm && !text.includes(searchTerm)) show = false;
            
            row.style.display = show ? '' : 'none';
        });
    }
    
    document.getElementById('categoryFilter').addEventListener('change', applyFilters);
    document.getElementById('stockFilter').addEventListener('change', applyFilters);
    document.getElementById('productSearch').addEventListener('keyup', applyFilters);
    
    // Clear filters
    document.getElementById('clearFilters').addEventListener('click', function() {
        document.getElementById('categoryFilter').value = '';
        document.getElementById('stockFilter').value = '';
        document.getElementById('productSearch').value = '';
        applyFilters();
    });
});
</script>
{% endblock %}
