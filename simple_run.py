#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة مبسطة لاختبار الواجهات
Simple version for testing interfaces
"""

from flask import Flask, render_template, request, redirect, url_for, flash, session
from datetime import datetime, date
import os

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'test-secret-key'

# بيانات تجريبية
sample_users = [
    {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'email': '<EMAIL>', 'role': 'admin', 'is_active': True, 'created_at': datetime.now(), 'last_login': datetime.now()},
    {'id': 2, 'username': 'accountant', 'full_name': 'المحاسب الرئيسي', 'email': '<EMAIL>', 'role': 'accountant', 'is_active': True, 'created_at': datetime.now(), 'last_login': None},
]

sample_accounts = [
    {'id': 1, 'account_code': '1001', 'account_name_ar': 'النقدية', 'account_name_en': 'Cash', 'account_type': 'assets', 'parent': None, 'is_active': True, 'created_at': datetime.now()},
    {'id': 2, 'account_code': '1002', 'account_name_ar': 'البنك', 'account_name_en': 'Bank', 'account_type': 'assets', 'parent': None, 'is_active': True, 'created_at': datetime.now()},
    {'id': 3, 'account_code': '2001', 'account_name_ar': 'الحسابات الدائنة', 'account_name_en': 'Accounts Payable', 'account_type': 'liabilities', 'parent': None, 'is_active': True, 'created_at': datetime.now()},
    {'id': 4, 'account_code': '4001', 'account_name_ar': 'مبيعات البضائع', 'account_name_en': 'Sales Revenue', 'account_type': 'revenue', 'parent': None, 'is_active': True, 'created_at': datetime.now()},
    {'id': 5, 'account_code': '5001', 'account_name_ar': 'الرواتب', 'account_name_en': 'Salaries', 'account_type': 'expenses', 'parent': None, 'is_active': True, 'created_at': datetime.now()},
]

sample_customers = [
    {'id': 1, 'customer_code': 'CUST001', 'name': 'شركة الأمل للتجارة', 'email': '<EMAIL>', 'phone': '+************', 'address': 'الرياض، المملكة العربية السعودية', 'tax_number': '***************', 'credit_limit': 50000, 'is_active': True, 'created_at': datetime.now()},
    {'id': 2, 'customer_code': 'CUST002', 'name': 'مؤسسة النجاح التجارية', 'email': '<EMAIL>', 'phone': '+************', 'address': 'جدة، المملكة العربية السعودية', 'tax_number': '***************', 'credit_limit': 75000, 'is_active': True, 'created_at': datetime.now()},
    {'id': 3, 'customer_code': 'CUST003', 'name': 'شركة التقدم للمقاولات', 'email': '<EMAIL>', 'phone': '+************', 'address': 'الدمام، المملكة العربية السعودية', 'tax_number': '', 'credit_limit': 25000, 'is_active': True, 'created_at': datetime.now()},
]

sample_suppliers = [
    {'id': 1, 'supplier_code': 'SUPP001', 'name': 'شركة المواد الأولية المحدودة', 'email': '<EMAIL>', 'phone': '+966501111111', 'address': 'الرياض، المملكة العربية السعودية', 'tax_number': '300000000000003', 'is_active': True, 'created_at': datetime.now()},
    {'id': 2, 'supplier_code': 'SUPP002', 'name': 'مؤسسة الخدمات اللوجستية', 'email': '<EMAIL>', 'phone': '+966502222222', 'address': 'جدة، المملكة العربية السعودية', 'tax_number': '300000000000004', 'is_active': True, 'created_at': datetime.now()},
]

sample_invoices = [
    {'id': 1, 'invoice_number': 'INV-2024-001', 'customer': {'name': 'شركة الأمل للتجارة'}, 'invoice_date': date.today(), 'total_amount': 15000, 'status': 'paid', 'created_at': datetime.now()},
    {'id': 2, 'invoice_number': 'INV-2024-002', 'customer': {'name': 'مؤسسة النجاح التجارية'}, 'invoice_date': date.today(), 'total_amount': 25000, 'status': 'sent', 'created_at': datetime.now()},
]

# متغير لتتبع المستخدم الحالي
current_user_data = {'is_authenticated': False, 'role': 'admin', 'full_name': 'مدير النظام'}

# الصفحة الرئيسية
@app.route('/')
def index():
    return render_template('dashboard.html',
                         total_customers=len(sample_customers),
                         total_suppliers=len(sample_suppliers),
                         total_products=15,
                         monthly_sales=65000,
                         recent_invoices=sample_invoices)

# تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        # تحقق بسيط
        if username == 'admin' and password == 'admin123':
            session['user_logged_in'] = True
            session['user_role'] = 'admin'
            session['user_name'] = 'مدير النظام'
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('auth/login.html')

# تسجيل الخروج
@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

# دليل الحسابات
@app.route('/accounts')
def accounts():
    return render_template('accounts/list.html', accounts=sample_accounts)

@app.route('/accounts/add', methods=['GET', 'POST'])
def add_account():
    if request.method == 'POST':
        flash('تم إضافة الحساب بنجاح', 'success')
        return redirect(url_for('accounts'))
    return render_template('accounts/add.html', parent_accounts=sample_accounts)

# العملاء
@app.route('/customers')
def customers():
    return render_template('customers/list.html', customers=sample_customers)

@app.route('/customers/add', methods=['GET', 'POST'])
def add_customer():
    if request.method == 'POST':
        flash('تم إضافة العميل بنجاح', 'success')
        return redirect(url_for('customers'))
    return render_template('customers/add.html')

# الموردين
@app.route('/suppliers')
def suppliers():
    return render_template('suppliers/list.html', suppliers=sample_suppliers)

@app.route('/suppliers/add', methods=['GET', 'POST'])
def add_supplier():
    if request.method == 'POST':
        flash('تم إضافة المورد بنجاح', 'success')
        return redirect(url_for('suppliers'))
    return render_template('suppliers/add.html')

# المستخدمين
@app.route('/users')
def users():
    return render_template('users/list.html', users=sample_users)

@app.route('/users/add', methods=['GET', 'POST'])
def add_user():
    if request.method == 'POST':
        flash('تم إضافة المستخدم بنجاح', 'success')
        return redirect(url_for('users'))
    return render_template('users/add.html')

# إضافة متغير للقوالب
@app.context_processor
def inject_user():
    return dict(current_user=type('obj', (object,), {
        'is_authenticated': session.get('user_logged_in', False),
        'role': session.get('user_role', 'user'),
        'full_name': session.get('user_name', 'مستخدم')
    })())

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
