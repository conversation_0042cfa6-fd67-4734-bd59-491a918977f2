#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة مبسطة لاختبار الواجهات
Simple version for testing interfaces
"""

from flask import Flask, render_template, request, redirect, url_for, flash, session
from datetime import datetime, date
import os

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'test-secret-key'

# بيانات تجريبية
sample_users = [
    {'id': 1, 'username': 'admin', 'full_name': 'مدير النظام', 'email': '<EMAIL>', 'role': 'admin', 'is_active': True, 'created_at': datetime.now(), 'last_login': datetime.now()},
    {'id': 2, 'username': 'accountant', 'full_name': 'المحاسب الرئيسي', 'email': '<EMAIL>', 'role': 'accountant', 'is_active': True, 'created_at': datetime.now(), 'last_login': None},
]

sample_accounts = [
    {'id': 1, 'account_code': '1001', 'account_name_ar': 'النقدية', 'account_name_en': 'Cash', 'account_type': 'assets', 'parent': None, 'is_active': True, 'created_at': datetime.now()},
    {'id': 2, 'account_code': '1002', 'account_name_ar': 'البنك', 'account_name_en': 'Bank', 'account_type': 'assets', 'parent': None, 'is_active': True, 'created_at': datetime.now()},
    {'id': 3, 'account_code': '2001', 'account_name_ar': 'الحسابات الدائنة', 'account_name_en': 'Accounts Payable', 'account_type': 'liabilities', 'parent': None, 'is_active': True, 'created_at': datetime.now()},
    {'id': 4, 'account_code': '4001', 'account_name_ar': 'مبيعات البضائع', 'account_name_en': 'Sales Revenue', 'account_type': 'revenue', 'parent': None, 'is_active': True, 'created_at': datetime.now()},
    {'id': 5, 'account_code': '5001', 'account_name_ar': 'الرواتب', 'account_name_en': 'Salaries', 'account_type': 'expenses', 'parent': None, 'is_active': True, 'created_at': datetime.now()},
]

sample_customers = [
    {'id': 1, 'customer_code': 'CUST001', 'name': 'شركة الأمل للتجارة', 'email': '<EMAIL>', 'phone': '+************', 'address': 'الرياض، المملكة العربية السعودية', 'tax_number': '***************', 'credit_limit': 50000, 'is_active': True, 'created_at': datetime.now()},
    {'id': 2, 'customer_code': 'CUST002', 'name': 'مؤسسة النجاح التجارية', 'email': '<EMAIL>', 'phone': '+************', 'address': 'جدة، المملكة العربية السعودية', 'tax_number': '***************', 'credit_limit': 75000, 'is_active': True, 'created_at': datetime.now()},
    {'id': 3, 'customer_code': 'CUST003', 'name': 'شركة التقدم للمقاولات', 'email': '<EMAIL>', 'phone': '+************', 'address': 'الدمام، المملكة العربية السعودية', 'tax_number': '', 'credit_limit': 25000, 'is_active': True, 'created_at': datetime.now()},
]

sample_suppliers = [
    {'id': 1, 'supplier_code': 'SUPP001', 'name': 'شركة المواد الأولية المحدودة', 'email': '<EMAIL>', 'phone': '+966501111111', 'address': 'الرياض، المملكة العربية السعودية', 'tax_number': '300000000000003', 'is_active': True, 'created_at': datetime.now()},
    {'id': 2, 'supplier_code': 'SUPP002', 'name': 'مؤسسة الخدمات اللوجستية', 'email': '<EMAIL>', 'phone': '+966502222222', 'address': 'جدة، المملكة العربية السعودية', 'tax_number': '300000000000004', 'is_active': True, 'created_at': datetime.now()},
]

sample_invoices = [
    {'id': 1, 'invoice_number': 'INV-2024-001', 'customer': {'name': 'شركة الأمل للتجارة'}, 'invoice_date': date.today(), 'total_amount': 15000, 'status': 'paid', 'created_at': datetime.now()},
    {'id': 2, 'invoice_number': 'INV-2024-002', 'customer': {'name': 'مؤسسة النجاح التجارية'}, 'invoice_date': date.today(), 'total_amount': 25000, 'status': 'sent', 'created_at': datetime.now()},
]

sample_products = [
    {'id': 1, 'product_code': 'PROD001', 'name_ar': 'لابتوب ديل', 'name_en': 'Dell Laptop', 'category': 'electronics', 'unit': 'قطعة', 'cost_price': 2500, 'selling_price': 3200, 'current_stock': 15, 'minimum_stock': 5, 'is_active': True, 'created_at': datetime.now()},
    {'id': 2, 'product_code': 'PROD002', 'name_ar': 'قميص قطني', 'name_en': 'Cotton Shirt', 'category': 'clothing', 'unit': 'قطعة', 'cost_price': 45, 'selling_price': 75, 'current_stock': 2, 'minimum_stock': 10, 'is_active': True, 'created_at': datetime.now()},
    {'id': 3, 'product_code': 'PROD003', 'name_ar': 'كتاب البرمجة', 'name_en': 'Programming Book', 'category': 'books', 'unit': 'قطعة', 'cost_price': 80, 'selling_price': 120, 'current_stock': 0, 'minimum_stock': 3, 'is_active': True, 'created_at': datetime.now()},
]

sample_journal_entries = [
    {'id': 1, 'entry_number': 'JE-2024-001', 'entry_date': date.today(), 'description': 'قيد افتتاحي', 'reference': 'REF-001', 'total_debit': 50000, 'total_credit': 50000, 'is_posted': True, 'creator': {'full_name': 'مدير النظام'}, 'lines': [{'account': '1001 - النقدية', 'debit': 50000, 'credit': 0}]},
    {'id': 2, 'entry_number': 'JE-2024-002', 'entry_date': date.today(), 'description': 'مبيعات نقدية', 'reference': 'INV-001', 'total_debit': 11500, 'total_credit': 11500, 'is_posted': False, 'creator': {'full_name': 'المحاسب'}, 'lines': [{'account': '1001 - النقدية', 'debit': 11500, 'credit': 0}]},
]

# متغير لتتبع المستخدم الحالي
current_user_data = {'is_authenticated': False, 'role': 'admin', 'full_name': 'مدير النظام'}

# الصفحة الرئيسية
@app.route('/')
def index():
    return render_template('dashboard.html',
                         total_customers=len(sample_customers),
                         total_suppliers=len(sample_suppliers),
                         total_products=15,
                         monthly_sales=65000,
                         recent_invoices=sample_invoices)

# تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        # تحقق بسيط
        if username == 'admin' and password == 'admin123':
            session['user_logged_in'] = True
            session['user_role'] = 'admin'
            session['user_name'] = 'مدير النظام'
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('auth/login.html')

# تسجيل الخروج
@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

# دليل الحسابات
@app.route('/accounts')
def accounts():
    return render_template('accounts/list.html', accounts=sample_accounts)

@app.route('/accounts/add', methods=['GET', 'POST'])
def add_account():
    if request.method == 'POST':
        flash('تم إضافة الحساب بنجاح', 'success')
        return redirect(url_for('accounts'))
    return render_template('accounts/add.html', parent_accounts=sample_accounts)

# العملاء
@app.route('/customers')
def customers():
    return render_template('customers/list.html', customers=sample_customers)

@app.route('/customers/add', methods=['GET', 'POST'])
def add_customer():
    if request.method == 'POST':
        flash('تم إضافة العميل بنجاح', 'success')
        return redirect(url_for('customers'))
    return render_template('customers/add.html')

# الموردين
@app.route('/suppliers')
def suppliers():
    return render_template('suppliers/list.html', suppliers=sample_suppliers)

@app.route('/suppliers/add', methods=['GET', 'POST'])
def add_supplier():
    if request.method == 'POST':
        flash('تم إضافة المورد بنجاح', 'success')
        return redirect(url_for('suppliers'))
    return render_template('suppliers/add.html')

# المستخدمين
@app.route('/users')
def users():
    return render_template('users/list.html', users=sample_users)

@app.route('/users/add', methods=['GET', 'POST'])
def add_user():
    if request.method == 'POST':
        flash('تم إضافة المستخدم بنجاح', 'success')
        return redirect(url_for('users'))
    return render_template('users/add.html')

# المنتجات
@app.route('/products')
def products():
    return render_template('products/list.html', products=sample_products)

@app.route('/products/add', methods=['GET', 'POST'])
def add_product():
    if request.method == 'POST':
        flash('تم إضافة المنتج بنجاح', 'success')
        return redirect(url_for('products'))
    return render_template('products/add.html', suppliers=sample_suppliers)

# القيود اليومية
@app.route('/journal')
def journal_entries():
    return render_template('journal/list.html', journal_entries=sample_journal_entries)

@app.route('/journal/add', methods=['GET', 'POST'])
def add_journal_entry():
    if request.method == 'POST':
        flash('تم إضافة القيد بنجاح', 'success')
        return redirect(url_for('journal_entries'))
    return render_template('journal/add.html', accounts=sample_accounts)

# التقارير
@app.route('/reports')
def reports():
    return render_template('reports/index.html',
                         total_revenue=125000,
                         total_expenses=85000,
                         total_assets=250000,
                         total_liabilities=75000,
                         total_equity=175000,
                         cash_inflows=95000,
                         cash_outflows=65000,
                         total_sales=125000,
                         total_purchases=85000,
                         inventory_value=45000,
                         low_stock_count=3,
                         out_of_stock_count=1,
                         inventory_movements=156)

# إضافة متغير للقوالب
@app.context_processor
def inject_user():
    return dict(current_user=type('obj', (object,), {
        'is_authenticated': session.get('user_logged_in', False),
        'role': session.get('user_role', 'user'),
        'full_name': session.get('user_name', 'مستخدم')
    })())

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
