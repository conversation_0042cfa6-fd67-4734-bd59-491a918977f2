{% extends "base.html" %}

{% block title %}تفاصيل المورد - نظام المحاسبة الشامل{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-truck me-2"></i>
        تفاصيل المورد: {{ supplier.name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('suppliers') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة لقائمة الموردين
            </a>
            <a href="#" class="btn btn-primary">
                <i class="fas fa-edit me-1"></i>
                تعديل
            </a>
            <a href="#" class="btn btn-success">
                <i class="fas fa-shopping-cart me-1"></i>
                فاتورة شراء جديدة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- معلومات المورد الأساسية -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-building me-2"></i>
                    المعلومات الأساسية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رمز المورد:</strong></td>
                                <td>{{ supplier.supplier_code }}</td>
                            </tr>
                            <tr>
                                <td><strong>اسم المورد:</strong></td>
                                <td>{{ supplier.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>البريد الإلكتروني:</strong></td>
                                <td>
                                    {% if supplier.email %}
                                        <a href="mailto:{{ supplier.email }}">{{ supplier.email }}</a>
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>رقم الهاتف:</strong></td>
                                <td>
                                    {% if supplier.phone %}
                                        <a href="tel:{{ supplier.phone }}">{{ supplier.phone }}</a>
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>الشخص المسؤول:</strong></td>
                                <td>{{ supplier.contact_person or '<span class="text-muted">غير محدد</span>'|safe }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الرقم الضريبي:</strong></td>
                                <td>{{ supplier.tax_number or '<span class="text-muted">غير محدد</span>'|safe }}</td>
                            </tr>
                            <tr>
                                <td><strong>نوع المورد:</strong></td>
                                <td>
                                    {% if supplier.supplier_type == 'local' %}
                                        <span class="badge bg-success">محلي</span>
                                    {% elif supplier.supplier_type == 'international' %}
                                        <span class="badge bg-primary">دولي</span>
                                    {% elif supplier.supplier_type == 'manufacturer' %}
                                        <span class="badge bg-info">مصنع</span>
                                    {% elif supplier.supplier_type == 'distributor' %}
                                        <span class="badge bg-warning">موزع</span>
                                    {% elif supplier.supplier_type == 'service' %}
                                        <span class="badge bg-secondary">خدمات</span>
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>شروط الدفع:</strong></td>
                                <td>
                                    {% if supplier.payment_terms == 'cash' %}
                                        نقداً
                                    {% elif supplier.payment_terms == 'net_30' %}
                                        30 يوم
                                    {% elif supplier.payment_terms == 'net_60' %}
                                        60 يوم
                                    {% elif supplier.payment_terms == 'net_90' %}
                                        90 يوم
                                    {% elif supplier.payment_terms == 'custom' %}
                                        مخصص
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    {% if supplier.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-danger">غير نشط</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الإضافة:</strong></td>
                                <td>{{ supplier.created_at.strftime('%Y-%m-%d') if supplier.created_at else 'غير محدد' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if supplier.address %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6><strong>العنوان:</strong></h6>
                        <p class="text-muted">{{ supplier.address }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- فواتير الشراء -->
        <div class="card mt-4">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0">
                            <i class="fas fa-shopping-cart me-2"></i>
                            فواتير الشراء
                        </h5>
                    </div>
                    <div class="col-auto">
                        <a href="#" class="btn btn-sm btn-success">
                            <i class="fas fa-plus me-1"></i>
                            فاتورة شراء جديدة
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if supplier.purchase_invoices %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in supplier.purchase_invoices %}
                            <tr>
                                <td><strong>{{ invoice.invoice_number }}</strong></td>
                                <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ "%.2f"|format(invoice.total_amount) }} ريال</td>
                                <td>
                                    {% if invoice.status == 'draft' %}
                                        <span class="badge bg-secondary">مسودة</span>
                                    {% elif invoice.status == 'received' %}
                                        <span class="badge bg-primary">مستلمة</span>
                                    {% elif invoice.status == 'paid' %}
                                        <span class="badge bg-success">مدفوعة</span>
                                    {% elif invoice.status == 'cancelled' %}
                                        <span class="badge bg-danger">ملغية</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-success" title="طباعة">
                                            <i class="fas fa-print"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد فواتير شراء لهذا المورد</p>
                    <a href="#" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>
                        إنشاء فاتورة شراء جديدة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- الإحصائيات والمعلومات الجانبية -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات المورد
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center mb-3">
                    <div class="col-6">
                        <h3 class="text-primary">{{ supplier.purchase_invoices|length if supplier.purchase_invoices else 0 }}</h3>
                        <small class="text-muted">فواتير الشراء</small>
                    </div>
                    <div class="col-6">
                        <h3 class="text-success">{{ "%.0f"|format(supplier.total_purchases or 0) }}</h3>
                        <small class="text-muted">إجمالي المشتريات</small>
                    </div>
                </div>
                
                <div class="row text-center mb-3">
                    <div class="col-6">
                        <h3 class="text-warning">{{ "%.0f"|format(supplier.outstanding_amount or 0) }}</h3>
                        <small class="text-muted">المبلغ المستحق</small>
                    </div>
                    <div class="col-6">
                        <h3 class="text-info">{{ supplier.average_payment_days or 0 }}</h3>
                        <small class="text-muted">أيام الدفع المتوسط</small>
                    </div>
                </div>
                
                <!-- تقييم المورد -->
                <div class="mb-3">
                    <label class="form-label">تقييم المورد</label>
                    <div class="d-flex align-items-center">
                        {% set rating = supplier.rating or 0 %}
                        {% for i in range(1, 6) %}
                            <i class="fas fa-star {% if i <= rating %}text-warning{% else %}text-muted{% endif %} me-1"></i>
                        {% endfor %}
                        <span class="ms-2 text-muted">({{ rating }}/5)</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    آخر النشاطات
                </h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">آخر فاتورة شراء</h6>
                            <p class="timeline-text">{{ supplier.last_purchase_date.strftime('%Y-%m-%d') if supplier.last_purchase_date else 'لا توجد فواتير' }}</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">آخر دفعة</h6>
                            <p class="timeline-text">{{ supplier.last_payment_date.strftime('%Y-%m-%d') if supplier.last_payment_date else 'لا توجد دفعات' }}</p>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">تاريخ الإضافة</h6>
                            <p class="timeline-text">{{ supplier.created_at.strftime('%Y-%m-%d') if supplier.created_at else 'غير محدد' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="#" class="btn btn-outline-success">
                        <i class="fas fa-shopping-cart me-1"></i>
                        فاتورة شراء جديدة
                    </a>
                    <a href="#" class="btn btn-outline-primary">
                        <i class="fas fa-money-bill me-1"></i>
                        تسجيل دفعة
                    </a>
                    <a href="#" class="btn btn-outline-info">
                        <i class="fas fa-file-alt me-1"></i>
                        كشف حساب
                    </a>
                    <a href="#" class="btn btn-outline-warning">
                        <i class="fas fa-edit me-1"></i>
                        تعديل البيانات
                    </a>
                    <a href="#" class="btn btn-outline-secondary">
                        <i class="fas fa-star me-1"></i>
                        تقييم المورد
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 17px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}

.timeline-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.timeline-text {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0;
}
</style>
{% endblock %}
