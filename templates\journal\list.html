{% extends "base.html" %}

{% block title %}القيود اليومية - نظام المحاسبة الشامل{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-journal-whills me-2"></i>
        القيود اليومية
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="#" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                قيد جديد
            </a>
            <button type="button" class="btn btn-outline-secondary">
                <i class="fas fa-download me-1"></i>
                تصدير
            </button>
            <button type="button" class="btn btn-outline-info">
                <i class="fas fa-file-alt me-1"></i>
                دفتر اليومية
            </button>
        </div>
    </div>
</div>

<!-- فلاتر القيود -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-end">
                    <div class="col-md-3">
                        <label for="dateFrom" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="dateFrom">
                    </div>
                    <div class="col-md-3">
                        <label for="dateTo" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="dateTo">
                    </div>
                    <div class="col-md-2">
                        <label for="statusFilter" class="form-label">الحالة</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">جميع القيود</option>
                            <option value="draft">مسودة</option>
                            <option value="posted">مرحل</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="entrySearch" class="form-label">البحث</label>
                        <input type="text" class="form-control" id="entrySearch" placeholder="البحث في القيود...">
                    </div>
                    <div class="col-md-1">
                        <button type="button" class="btn btn-outline-secondary w-100" id="clearFilters">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة القيود اليومية
                </h5>
            </div>
            <div class="col-auto">
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-secondary" id="selectAll">تحديد الكل</button>
                    <button type="button" class="btn btn-outline-primary" id="postSelected">ترحيل المحدد</button>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        {% if journal_entries %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th width="40">
                            <input type="checkbox" class="form-check-input" id="selectAllCheckbox">
                        </th>
                        <th>رقم القيد</th>
                        <th>التاريخ</th>
                        <th>البيان</th>
                        <th>المرجع</th>
                        <th>إجمالي المدين</th>
                        <th>إجمالي الدائن</th>
                        <th>الحالة</th>
                        <th>المنشئ</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for entry in journal_entries %}
                    <tr class="entry-row" data-status="{{ entry.status or 'draft' }}">
                        <td>
                            <input type="checkbox" class="form-check-input entry-checkbox" value="{{ entry.id }}" 
                                   {% if entry.is_posted %}disabled{% endif %}>
                        </td>
                        <td>
                            <strong>{{ entry.entry_number }}</strong>
                        </td>
                        <td>{{ entry.entry_date.strftime('%Y-%m-%d') if entry.entry_date else '' }}</td>
                        <td>
                            <div class="entry-description">
                                {{ entry.description[:50] }}{% if entry.description|length > 50 %}...{% endif %}
                            </div>
                            {% if entry.lines %}
                            <small class="text-muted">{{ entry.lines|length }} سطر</small>
                            {% endif %}
                        </td>
                        <td>{{ entry.reference or '-' }}</td>
                        <td class="text-end">
                            <span class="text-primary">{{ "%.2f"|format(entry.total_debit) }}</span>
                        </td>
                        <td class="text-end">
                            <span class="text-warning">{{ "%.2f"|format(entry.total_credit) }}</span>
                        </td>
                        <td>
                            {% if entry.is_posted %}
                                <span class="badge bg-success">مرحل</span>
                            {% else %}
                                <span class="badge bg-secondary">مسودة</span>
                            {% endif %}
                            
                            {% if entry.total_debit != entry.total_credit %}
                                <span class="badge bg-danger ms-1" title="القيد غير متوازن">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <small>{{ entry.creator.full_name if entry.creator else 'غير محدد' }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" title="عرض" onclick="viewEntry({{ entry.id }})">
                                    <i class="fas fa-eye"></i>
                                </button>
                                {% if not entry.is_posted %}
                                <button type="button" class="btn btn-outline-success" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info" title="ترحيل" onclick="postEntry({{ entry.id }})">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" title="حذف" onclick="deleteEntry({{ entry.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% else %}
                                <button type="button" class="btn btn-outline-secondary" title="طباعة">
                                    <i class="fas fa-print"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="table-secondary">
                        <th colspan="5">الإجمالي</th>
                        <th class="text-end">{{ "%.2f"|format(journal_entries|sum(attribute='total_debit')) }}</th>
                        <th class="text-end">{{ "%.2f"|format(journal_entries|sum(attribute='total_credit')) }}</th>
                        <th colspan="3"></th>
                    </tr>
                </tfoot>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-journal-whills fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد قيود يومية</h5>
            <p class="text-muted">ابدأ بإنشاء قيد يومي جديد</p>
            <a href="{{ url_for('add_journal_entry') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                قيد جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- إحصائيات القيود -->
{% if journal_entries %}
<div class="row mt-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h5 class="card-title">إجمالي القيود</h5>
                        <h3>{{ journal_entries|length }}</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-journal-whills fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h5 class="card-title">قيود مرحلة</h5>
                        <h3>{{ journal_entries|selectattr('is_posted', 'equalto', true)|list|length }}</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h5 class="card-title">مسودات</h5>
                        <h3>{{ journal_entries|rejectattr('is_posted')|list|length }}</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-edit fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h5 class="card-title">إجمالي المبلغ</h5>
                        <h3>{{ "%.0f"|format(journal_entries|sum(attribute='total_debit')) }}</h3>
                    </div>
                    <div class="flex-shrink-0">
                        <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Entry Details Modal -->
<div class="modal fade" id="entryModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل القيد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="entryDetails">
                <!-- Entry details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary">تعديل</button>
                <button type="button" class="btn btn-success">طباعة</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function viewEntry(entryId) {
    // Load entry details via AJAX
    document.getElementById('entryDetails').innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    `;
    
    var entryModal = new bootstrap.Modal(document.getElementById('entryModal'));
    entryModal.show();
    
    // Simulate loading entry details
    setTimeout(() => {
        document.getElementById('entryDetails').innerHTML = `
            <div class="row mb-3">
                <div class="col-md-6">
                    <h6>معلومات القيد</h6>
                    <p><strong>رقم القيد:</strong> JE-2024-001</p>
                    <p><strong>التاريخ:</strong> 2024-01-15</p>
                    <p><strong>البيان:</strong> قيد افتتاحي</p>
                </div>
                <div class="col-md-6">
                    <h6>معلومات إضافية</h6>
                    <p><strong>المرجع:</strong> REF-001</p>
                    <p><strong>الحالة:</strong> مرحل</p>
                    <p><strong>المنشئ:</strong> مدير النظام</p>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>الحساب</th>
                            <th>البيان</th>
                            <th>مدين</th>
                            <th>دائن</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1001 - النقدية</td>
                            <td>رصيد افتتاحي</td>
                            <td class="text-end">10,000.00</td>
                            <td class="text-end">-</td>
                        </tr>
                        <tr>
                            <td>3001 - رأس المال</td>
                            <td>رصيد افتتاحي</td>
                            <td class="text-end">-</td>
                            <td class="text-end">10,000.00</td>
                        </tr>
                    </tbody>
                    <tfoot>
                        <tr class="table-secondary">
                            <th colspan="2">الإجمالي</th>
                            <th class="text-end">10,000.00</th>
                            <th class="text-end">10,000.00</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        `;
    }, 1000);
}

function postEntry(entryId) {
    if (confirm('هل أنت متأكد من ترحيل هذا القيد؟ لن تتمكن من تعديله بعد الترحيل.')) {
        // Here you would send an AJAX request to post the entry
        alert('تم ترحيل القيد بنجاح');
        location.reload();
    }
}

function deleteEntry(entryId) {
    if (confirm('هل أنت متأكد من حذف هذا القيد؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        // Here you would send an AJAX request to delete the entry
        alert('تم حذف القيد بنجاح');
        location.reload();
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Set default date range (current month)
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    document.getElementById('dateFrom').value = firstDay.toISOString().split('T')[0];
    document.getElementById('dateTo').value = today.toISOString().split('T')[0];
    
    // Filters
    function applyFilters() {
        const dateFrom = document.getElementById('dateFrom').value;
        const dateTo = document.getElementById('dateTo').value;
        const status = document.getElementById('statusFilter').value;
        const searchTerm = document.getElementById('entrySearch').value.toLowerCase();
        
        document.querySelectorAll('.entry-row').forEach(row => {
            let show = true;
            
            // Date filter would be implemented with actual dates
            // Status filter
            if (status && row.dataset.status !== status) show = false;
            
            // Search filter
            if (searchTerm && !row.textContent.toLowerCase().includes(searchTerm)) show = false;
            
            row.style.display = show ? '' : 'none';
        });
    }
    
    document.getElementById('dateFrom').addEventListener('change', applyFilters);
    document.getElementById('dateTo').addEventListener('change', applyFilters);
    document.getElementById('statusFilter').addEventListener('change', applyFilters);
    document.getElementById('entrySearch').addEventListener('keyup', applyFilters);
    
    // Clear filters
    document.getElementById('clearFilters').addEventListener('click', function() {
        document.getElementById('dateFrom').value = '';
        document.getElementById('dateTo').value = '';
        document.getElementById('statusFilter').value = '';
        document.getElementById('entrySearch').value = '';
        applyFilters();
    });
    
    // Select all functionality
    document.getElementById('selectAllCheckbox').addEventListener('change', function() {
        document.querySelectorAll('.entry-checkbox:not(:disabled)').forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
    
    // Post selected entries
    document.getElementById('postSelected').addEventListener('click', function() {
        const selected = Array.from(document.querySelectorAll('.entry-checkbox:checked')).map(cb => cb.value);
        if (selected.length === 0) {
            alert('يرجى تحديد قيود للترحيل');
            return;
        }
        
        if (confirm(`هل أنت متأكد من ترحيل ${selected.length} قيد؟`)) {
            alert('تم ترحيل القيود المحددة بنجاح');
            location.reload();
        }
    });
});
</script>
{% endblock %}
