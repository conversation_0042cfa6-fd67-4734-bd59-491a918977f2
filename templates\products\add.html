{% extends "base.html" %}

{% block title %}إضافة منتج جديد - نظام المحاسبة الشامل{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-plus me-2"></i>
        إضافة منتج جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('products') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة لقائمة المنتجات
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-box me-2"></i>
                    بيانات المنتج
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="productForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="product_code" class="form-label">رمز المنتج <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-barcode"></i>
                                </span>
                                <input type="text" class="form-control" id="product_code" name="product_code" required>
                                <button class="btn btn-outline-secondary" type="button" id="generateCode">
                                    <i class="fas fa-magic"></i>
                                </button>
                            </div>
                            <div class="form-text">رمز فريد للمنتج (مثال: PROD001)</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label">الفئة</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">اختر الفئة</option>
                                <option value="electronics">إلكترونيات</option>
                                <option value="clothing">ملابس</option>
                                <option value="food">أغذية</option>
                                <option value="books">كتب</option>
                                <option value="furniture">أثاث</option>
                                <option value="tools">أدوات</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name_ar" class="form-label">اسم المنتج (عربي) <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-tag"></i>
                                </span>
                                <input type="text" class="form-control" id="name_ar" name="name_ar" required>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="name_en" class="form-label">اسم المنتج (إنجليزي)</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-tag"></i>
                                </span>
                                <input type="text" class="form-control" id="name_en" name="name_en">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label for="description" class="form-label">وصف المنتج</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="وصف مفصل للمنتج"></textarea>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="unit" class="form-label">الوحدة <span class="text-danger">*</span></label>
                            <select class="form-select" id="unit" name="unit" required>
                                <option value="">اختر الوحدة</option>
                                <option value="قطعة">قطعة</option>
                                <option value="كيلو">كيلو</option>
                                <option value="جرام">جرام</option>
                                <option value="لتر">لتر</option>
                                <option value="متر">متر</option>
                                <option value="صندوق">صندوق</option>
                                <option value="عبوة">عبوة</option>
                                <option value="دزينة">دزينة</option>
                            </select>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="cost_price" class="form-label">سعر التكلفة (ريال) <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-dollar-sign"></i>
                                </span>
                                <input type="number" class="form-control" id="cost_price" name="cost_price" min="0" step="0.01" required>
                                <span class="input-group-text">ريال</span>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="selling_price" class="form-label">سعر البيع (ريال) <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-dollar-sign"></i>
                                </span>
                                <input type="number" class="form-control" id="selling_price" name="selling_price" min="0" step="0.01" required>
                                <span class="input-group-text">ريال</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="current_stock" class="form-label">المخزون الحالي</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-warehouse"></i>
                                </span>
                                <input type="number" class="form-control" id="current_stock" name="current_stock" min="0" step="0.001" value="0">
                                <span class="input-group-text" id="stockUnit">وحدة</span>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="minimum_stock" class="form-label">الحد الأدنى للمخزون</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </span>
                                <input type="number" class="form-control" id="minimum_stock" name="minimum_stock" min="0" step="0.001" value="0">
                                <span class="input-group-text" id="minStockUnit">وحدة</span>
                            </div>
                            <div class="form-text">سيتم تنبيهك عند الوصول لهذا الحد</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="supplier_id" class="form-label">المورد الرئيسي</label>
                            <select class="form-select" id="supplier_id" name="supplier_id">
                                <option value="">اختر المورد</option>
                                {% for supplier in suppliers %}
                                <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                <label class="form-check-label" for="is_active">
                                    المنتج نشط
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="track_stock" name="track_stock" checked>
                                <label class="form-check-label" for="track_stock">
                                    تتبع المخزون
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('products') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="button" class="btn btn-outline-primary me-md-2" id="saveAndNew">
                            <i class="fas fa-plus me-1"></i>
                            حفظ وإضافة آخر
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ المنتج
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calculator me-2"></i>
                    حاسبة الربح
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-12 mb-3">
                        <h6>هامش الربح</h6>
                        <h3 class="text-success" id="profitMargin">0.00%</h3>
                    </div>
                </div>
                <div class="row text-center">
                    <div class="col-6">
                        <small class="text-muted">الربح لكل وحدة</small>
                        <div class="fw-bold text-primary" id="profitPerUnit">0.00 ريال</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">نسبة الربح</small>
                        <div class="fw-bold text-info" id="profitRatio">0.00%</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    نصائح
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6 class="alert-heading">
                        <i class="fas fa-lightbulb me-1"></i>
                        نصائح مهمة
                    </h6>
                    <ul class="mb-0">
                        <li>استخدم رموز واضحة للمنتجات</li>
                        <li>حدد سعر التكلفة بدقة</li>
                        <li>اختر الوحدة المناسبة</li>
                        <li>حدد الحد الأدنى للمخزون</li>
                        <li>اربط المنتج بالمورد الرئيسي</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tags me-2"></i>
                    الفئات المتاحة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex flex-wrap gap-2">
                    <span class="badge bg-primary">إلكترونيات</span>
                    <span class="badge bg-success">ملابس</span>
                    <span class="badge bg-warning">أغذية</span>
                    <span class="badge bg-info">كتب</span>
                    <span class="badge bg-secondary">أثاث</span>
                    <span class="badge bg-dark">أدوات</span>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    آخر المنتجات المضافة
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">منتج تجريبي 1</h6>
                            <small class="text-muted">PROD001</small>
                        </div>
                        <small class="text-muted">اليوم</small>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">منتج تجريبي 2</h6>
                            <small class="text-muted">PROD002</small>
                        </div>
                        <small class="text-muted">أمس</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Generate product code automatically
    document.getElementById('generateCode').addEventListener('click', function() {
        const timestamp = Date.now().toString().slice(-6);
        const code = 'PROD' + timestamp;
        document.getElementById('product_code').value = code;
    });
    
    // Auto-generate code when name is entered
    document.getElementById('name_ar').addEventListener('blur', function() {
        const codeField = document.getElementById('product_code');
        if (!codeField.value && this.value) {
            const name = this.value.trim();
            const words = name.split(' ');
            let code = 'PROD';
            
            // Take first 2 letters from each word (max 2 words)
            for (let i = 0; i < Math.min(words.length, 2); i++) {
                if (words[i].length >= 2) {
                    code += words[i].substring(0, 2).toUpperCase();
                }
            }
            
            // Add random number
            code += Math.floor(Math.random() * 1000).toString().padStart(3, '0');
            codeField.value = code;
        }
    });
    
    // Update unit display
    document.getElementById('unit').addEventListener('change', function() {
        const unit = this.value || 'وحدة';
        document.getElementById('stockUnit').textContent = unit;
        document.getElementById('minStockUnit').textContent = unit;
    });
    
    // Calculate profit margin
    function calculateProfit() {
        const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
        const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;
        
        if (costPrice > 0 && sellingPrice > 0) {
            const profit = sellingPrice - costPrice;
            const profitMargin = (profit / sellingPrice) * 100;
            const profitRatio = (profit / costPrice) * 100;
            
            document.getElementById('profitPerUnit').textContent = profit.toFixed(2) + ' ريال';
            document.getElementById('profitMargin').textContent = profitMargin.toFixed(2) + '%';
            document.getElementById('profitRatio').textContent = profitRatio.toFixed(2) + '%';
            
            // Color coding
            if (profitMargin < 10) {
                document.getElementById('profitMargin').className = 'text-danger';
            } else if (profitMargin < 20) {
                document.getElementById('profitMargin').className = 'text-warning';
            } else {
                document.getElementById('profitMargin').className = 'text-success';
            }
        } else {
            document.getElementById('profitPerUnit').textContent = '0.00 ريال';
            document.getElementById('profitMargin').textContent = '0.00%';
            document.getElementById('profitRatio').textContent = '0.00%';
        }
    }
    
    document.getElementById('cost_price').addEventListener('input', calculateProfit);
    document.getElementById('selling_price').addEventListener('input', calculateProfit);
    
    // Auto-suggest selling price (cost + 30% margin)
    document.getElementById('cost_price').addEventListener('blur', function() {
        const sellingPriceField = document.getElementById('selling_price');
        if (!sellingPriceField.value && this.value) {
            const costPrice = parseFloat(this.value);
            const suggestedPrice = costPrice * 1.3; // 30% margin
            sellingPriceField.value = suggestedPrice.toFixed(2);
            calculateProfit();
        }
    });
    
    // Save and add new functionality
    document.getElementById('saveAndNew').addEventListener('click', function() {
        const form = document.getElementById('productForm');
        if (form.checkValidity()) {
            // Here you would submit the form via AJAX
            alert('تم حفظ المنتج بنجاح! يمكنك إضافة منتج آخر.');
            form.reset();
            document.getElementById('is_active').checked = true;
            document.getElementById('track_stock').checked = true;
            calculateProfit();
        } else {
            form.reportValidity();
        }
    });
    
    // Form validation
    document.getElementById('productForm').addEventListener('submit', function(e) {
        const productCode = document.getElementById('product_code').value;
        const nameAr = document.getElementById('name_ar').value;
        const unit = document.getElementById('unit').value;
        const costPrice = document.getElementById('cost_price').value;
        const sellingPrice = document.getElementById('selling_price').value;
        
        if (!productCode || !nameAr || !unit || !costPrice || !sellingPrice) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
            return false;
        }
        
        if (parseFloat(sellingPrice) <= parseFloat(costPrice)) {
            if (!confirm('سعر البيع أقل من أو يساوي سعر التكلفة. هل تريد المتابعة؟')) {
                e.preventDefault();
                return false;
            }
        }
    });
});
</script>
{% endblock %}
