{% extends "base.html" %}

{% block title %}تعديل المستخدم - نظام المحاسبة الشامل{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-edit me-2"></i>
        تعديل المستخدم: {{ user.full_name }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('users') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-circle me-2"></i>
                    تعديل بيانات المستخدم
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="userForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                                <input type="text" class="form-control" id="username" name="username" value="{{ user.username }}" required readonly>
                            </div>
                            <div class="form-text">اسم المستخدم لا يمكن تعديله</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input type="email" class="form-control" id="email" name="email" value="{{ user.email }}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="full_name" class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-id-card"></i>
                                </span>
                                <input type="text" class="form-control" id="full_name" name="full_name" value="{{ user.full_name }}" required>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="role" class="form-label">الدور <span class="text-danger">*</span></label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="">اختر الدور</option>
                                <option value="admin" {% if user.role == 'admin' %}selected{% endif %}>مدير النظام</option>
                                <option value="accountant" {% if user.role == 'accountant' %}selected{% endif %}>محاسب</option>
                                <option value="user" {% if user.role == 'user' %}selected{% endif %}>مستخدم عادي</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="change_password" name="change_password">
                                <label class="form-check-label" for="change_password">
                                    تغيير كلمة المرور
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div id="passwordSection" style="display: none;">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">كلمة المرور الجديدة</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="password" name="password">
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">يجب أن تكون 8 أحرف على الأقل</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {% if user.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">
                                    المستخدم نشط
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('users') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ التعديلات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات المستخدم
                </h5>
            </div>
            <div class="card-body">
                <p><strong>تاريخ الإنشاء:</strong><br>{{ user.created_at.strftime('%Y-%m-%d %H:%M') if user.created_at else 'غير محدد' }}</p>
                <p><strong>آخر تسجيل دخول:</strong><br>{{ user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else 'لم يسجل دخول' }}</p>
                <p><strong>عدد مرات الدخول:</strong><br>{{ user.login_count or 0 }}</p>
                <p><strong>الحالة:</strong><br>
                    {% if user.is_active %}
                        <span class="badge bg-success">نشط</span>
                    {% else %}
                        <span class="badge bg-danger">غير نشط</span>
                    {% endif %}
                </p>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات النشاط
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ user.created_entries or 0 }}</h4>
                        <small class="text-muted">القيود المنشأة</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ user.created_invoices or 0 }}</h4>
                        <small class="text-muted">الفواتير المنشأة</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-info">{{ user.created_customers or 0 }}</h4>
                        <small class="text-muted">العملاء المضافين</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning">{{ user.created_suppliers or 0 }}</h4>
                        <small class="text-muted">الموردين المضافين</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    الصلاحيات
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-danger">
                        <i class="fas fa-crown me-1"></i>
                        مدير النظام
                    </h6>
                    <small class="text-muted">
                        صلاحيات كاملة لجميع أجزاء النظام بما في ذلك إدارة المستخدمين والإعدادات
                    </small>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-warning">
                        <i class="fas fa-calculator me-1"></i>
                        محاسب
                    </h6>
                    <small class="text-muted">
                        صلاحيات محاسبية كاملة: إدارة الحسابات، الفواتير، التقارير المالية
                    </small>
                </div>
                
                <div class="mb-3">
                    <h6 class="text-secondary">
                        <i class="fas fa-user me-1"></i>
                        مستخدم عادي
                    </h6>
                    <small class="text-muted">
                        صلاحيات محدودة: عرض التقارير الأساسية وإدخال البيانات
                    </small>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تحذير
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <small>
                        <i class="fas fa-info-circle me-1"></i>
                        تأكد من صحة البيانات قبل الحفظ. تعديل دور المستخدم قد يؤثر على صلاحياته في النظام.
                    </small>
                </div>
                
                {% if user.id == current_user.id %}
                <div class="alert alert-info">
                    <small>
                        <i class="fas fa-info-circle me-1"></i>
                        أنت تقوم بتعديل بياناتك الشخصية. لا يمكنك تغيير دورك أو إلغاء تفعيل حسابك.
                    </small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show/hide password section
    document.getElementById('change_password').addEventListener('change', function() {
        const passwordSection = document.getElementById('passwordSection');
        const passwordField = document.getElementById('password');
        const confirmPasswordField = document.getElementById('confirm_password');
        
        if (this.checked) {
            passwordSection.style.display = 'block';
            passwordField.required = true;
            confirmPasswordField.required = true;
        } else {
            passwordSection.style.display = 'none';
            passwordField.required = false;
            confirmPasswordField.required = false;
            passwordField.value = '';
            confirmPasswordField.value = '';
        }
    });
    
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordField = document.getElementById('password');
        const icon = this.querySelector('i');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
    
    // Prevent self-role change and self-deactivation
    {% if user.id == current_user.id %}
    document.getElementById('role').disabled = true;
    document.getElementById('is_active').disabled = true;
    {% endif %}
    
    // Form validation
    document.getElementById('userForm').addEventListener('submit', function(e) {
        const email = document.getElementById('email').value;
        const fullName = document.getElementById('full_name').value;
        const changePassword = document.getElementById('change_password').checked;
        
        if (!email || !fullName) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
            return false;
        }
        
        if (changePassword) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (!password || !confirmPassword) {
                e.preventDefault();
                alert('يرجى إدخال كلمة المرور وتأكيدها');
                return false;
            }
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
                return false;
            }
            
            if (password.length < 8) {
                e.preventDefault();
                alert('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
                return false;
            }
        }
    });
});
</script>
{% endblock %}
